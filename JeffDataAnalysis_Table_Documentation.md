# JeffDataAnalysis Table Documentation

## Overview
The `JeffDataAnalysis` table is a comprehensive data store for Amazon product analysis and audit information. It captures detailed product data, seller information, performance metrics, and analysis results for competitive intelligence and optimization recommendations.

## Table Structure

### Primary Key
- **id** (Int, Auto-increment): Unique identifier for each record

### Seller Information
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `sellerId` | String | Amazon seller identifier | "A1B2C3D4E5" |
| `marketplace` | String (nullable) | Amazon marketplace country | "US", "UK", "DE" |
| `sellerUrl` | String (nullable) | Direct link to seller storefront | "https://amazon.com/shops/seller" |

### Basic Product Information
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `url` | String | Product page URL | "https://amazon.com/dp/B08N5WRWNW" |
| `brand_name` | String (255 chars) | Brand name of the product | "Apple", "Samsung" |
| `product_title` | Text | Full product title from Amazon | "iPhone 13 Pro Max 128GB" |
| `description` | Text | Product description text | "Latest iPhone with advanced camera..." |
| `price` | Decimal (10,2) | Current product price | 999.99 |

### Ratings & Reviews
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `rating` | Decimal (3,2) | Average star rating | 4.5 |
| `total_reviews` | Int | Total number of reviews | 1250 |
| `review_category` | String (100 chars) | Category of review analysis | "Electronics", "Home & Garden" |
| `star_5_count` | Int | Count of 5-star reviews | 800 |
| `star_4_count` | Int | Count of 4-star reviews | 300 |
| `star_3_count` | Int | Count of 3-star reviews | 100 |
| `star_2_count` | Int | Count of 2-star reviews | 30 |
| `star_1_count` | Int | Count of 1-star reviews | 20 |
| `sales_count` | Int | Estimated sales volume | 5000 |

### Media Information
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `main_image_url` | Text | URL of main product image | "https://images.amazon.com/main.jpg" |
| `image_count` | Int | Total number of product images | 8 |
| `video_count` | Int | Number of product videos | 2 |

### Metadata & Validation
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `title_char_count` | Int | Character count of product title | 45 |
| `title_under_150_chars` | Boolean | Whether title is under 150 characters | true |
| `out_of_stock` | Boolean | Product availability status | false |

### Amazon Features
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `aplus_content_present` | Boolean | Has A+ content | true |
| `premium_aplus_present` | Boolean | Has premium A+ content | false |
| `brand_story_present` | Boolean | Has brand story section | true |
| `storefront_present` | Boolean | Has dedicated storefront | true |
| `storefront_url` | Text | Storefront URL if present | "https://amazon.com/stores/brand" |

### Revenue & Qualification
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `prospect_revenue` | Float | Estimated prospect revenue | 50000.0 |
| `revenue_difference_monthly` | Float | Monthly revenue difference vs competitor | 5000.0 |
| `revenue_difference_yearly` | Float | Yearly revenue difference vs competitor | 60000.0 |
| `revenue_source` | String (255 chars) | Source of revenue data | "Jungle Scout", "Helium 10" |

### Company & Job Information
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `company_name` | String (255 chars) | Company name | "TechCorp Inc." |
| `company_name_humanized` | Text | Human-readable company name | "TechCorp" |
| `campaign_name` | String (255 chars) | Marketing campaign identifier | "Q4_2024_Amazon_Audit" |

### Product Details
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `product_slug` | String (255 chars) | URL-friendly product identifier | "iphone-13-pro-max" |
| `product_amazon_url` | Text | Full Amazon product URL | "https://amazon.com/dp/B08N5WRWNW" |
| `product_title_humanized` | Text | Clean, readable product title | "iPhone 13 Pro Max" |
| `product_asin` | String (50 chars) | Amazon Standard Identification Number | "B08N5WRWNW" |
| `branded_keyword` | String (255 chars) | Brand-specific search term | "Apple iPhone" |
| `non_branded_keyword` | String (255 chars) | Generic search term | "smartphone" |

### JSON Data Fields
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `bullet_points` | Json | Product bullet points as JSON array | `["Feature 1", "Feature 2"]` |
| `categories_and_ranks` | Json | Category rankings as JSON object | `{"Electronics": 15, "Phones": 3}` |
| `secondary_images` | Json | Additional product images | `["img1.jpg", "img2.jpg"]` |
| `brand_story_images` | Json | Brand story related images | `["story1.jpg", "story2.jpg"]` |

### Flattened Category & Rank Fields
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `category_1` | String (255 chars) | Primary category | "Electronics" |
| `rank_1` | Int | Rank in primary category | 15 |
| `category_2` | String (255 chars) | Secondary category | "Cell Phones" |
| `rank_2` | Int | Rank in secondary category | 3 |

### Flattened Bullet Point Fields
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `bullet_point_1` | Text | First bullet point | "Advanced camera system" |
| `bullet_point_1_chars` | Int | Character count of first bullet | 25 |
| `bullet_point_2` | Text | Second bullet point | "5G capable" |
| `bullet_point_2_chars` | Int | Character count of second bullet | 12 |
| `bullet_point_3` | Text | Third bullet point | "Long battery life" |
| `bullet_point_3_chars` | Int | Character count of third bullet | 18 |
| `bullet_point_4` | Text | Fourth bullet point | "Water resistant" |
| `bullet_point_4_chars` | Int | Character count of fourth bullet | 16 |
| `bullet_points_all_caps` | Boolean | Whether all bullet points are uppercase | false |

### Competitor Information
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `competitor_product_url` | Text | Competitor product URL | "https://amazon.com/dp/competitor_asin" |
| `competitor_asin` | String (50 chars) | Competitor product ASIN | "B07XYZ1234" |
| `competitor_product_title` | Text | Competitor product title | "Samsung Galaxy S21" |
| `competitor_product_title_humanized` | Text | Clean competitor title | "Samsung Galaxy S21" |
| `competitor_brand_name` | String (255 chars) | Competitor brand name | "Samsung" |
| `competitor_brand_name_humanized` | Text | Clean competitor brand name | "Samsung" |
| `competitor_revenue` | Float | Competitor revenue estimate | 45000.0 |
| `competitor_annual_revenue` | Float | Competitor annual revenue | 540000.0 |

### Audit Analysis Fields
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `number_of_optimizations` | Int | Total optimization opportunities | 5 |
| `main_image_optimization` | Text | Image optimization recommendation | "Add lifestyle context" |
| `data_point_1` | Text | First key data insight | "High conversion rate" |
| `data_point_2` | Text | Second key data insight | "Strong brand presence" |
| `data_point_3` | Text | Third key data insight | "Competitive pricing" |
| `pain_point_1` | Text | First identified issue | "Poor image quality" |
| `pain_point_2` | Text | Second identified issue | "Weak bullet points" |
| `pain_point_3` | Text | Third identified issue | "Missing video content" |
| `top_improvement_1` | Text | Primary improvement suggestion | "Optimize main image" |
| `top_improvement_2` | Text | Secondary improvement suggestion | "Rewrite bullet points" |
| `top_improvement_3` | Text | Tertiary improvement suggestion | "Add product videos" |

### Timestamps
| Field | Type | Description | Example |
|-------|------|-------------|---------|
| `createdAt` | DateTime | Record creation timestamp | "2024-01-15T10:30:00Z" |
| `updatedAt` | DateTime | Last update timestamp | "2024-01-15T15:45:00Z" |

## Database Indexes
- **Primary Index**: `id` (auto-increment)
- **Performance Index**: `createdAt` (for time-based queries)

## Data Relationships
This table serves as a central repository for:
- Amazon product audit data
- Competitive analysis results
- Revenue and performance metrics
- Optimization recommendations
- Brand and product intelligence

## Usage Notes
- The table combines structured fields with JSON fields for flexibility
- Flattened fields provide easy querying for common data points
- Timestamps enable tracking of data freshness and analysis history
- Boolean flags quickly identify presence/absence of key features
- Decimal fields maintain precision for financial and rating data