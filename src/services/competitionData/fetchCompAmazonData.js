const { getHtmlByProxy } = require("../../utils/getHtmlByProxy");
const { getSalesEstimate } = require("../jungleScoutData/process");
const { completionFactory } = require("../scrapeGPT/factory");
const { getMultipliers } = require("./compFilter");
const getCompProductDetails = require("./getCompProductDetails");
const getCompWithAmazonData = require("./getCompWithAmazonData");

async function fetchCompAmazonData(
  amazonData,
  company_name,
  companyId,
  usage,
  clientId,
  TARGET_URL,
  competitorUrl
) {
  try {
    // console.log("AMAZON DATA:", amazonData);
    let prospectRevenue = 0;
    let prospectPrice = 0;
    let prospectProductLink = "";
    let prospectProductName = "";
    let keywordResponse = null;
    let searchData = null;
    let keyword = null;
    let prospectData = null;
    let compData = null;
    let prospectRevenueSource = null;
    let compSearchUrl = null;
    let ogAsin = "";

    let invalidKeyword = false;

    for (let i = 0; i < amazonData["productData"].length; i++) {
      prospectData = amazonData["productData"][i];
      console.log(
        "Processing competition data for product:" + prospectData.asin
      );
      prospectProductLink = `${TARGET_URL}/dp/${prospectData.asin}`;
      prospectPrice = prospectData.price || "0";
      prospectProductName = prospectData.productTitle.value;
      const sales = prospectData.sales;
      prospectRevenue =
        isNaN(sales) || isNaN(prospectPrice) ? 0 : sales * prospectPrice;
      ogAsin = prospectData.asin;

      if (prospectRevenue <= 0) {
        // const jungleScoutData = await getSalesEstimate(
        //   prospectData.asin,
        //   clientId
        // );
        // // console.log("JUNGLE SCOUT DATA:", jungleScoutData);
        // prospectRevenue = jungleScoutData.revenue;
        // if (jungleScoutData.Units > 0) {
        //   prospectPrice = jungleScoutData.revenue / jungleScoutData.Units;
        // }
        // prospectRevenueSource = jungleScoutData.status;
      } else {
        prospectRevenueSource = "The 30-day sales number on Amazon";
      }

      if (competitorUrl) {
        let competitorRevenue = 0;
        let competitorProductName = "";
        let competitorName = "";
        let competitorProductAmazonURL = competitorUrl;
        const productPage = await getHtmlByProxy(
          competitorProductAmazonURL,
          clientId,
          3,
          companyId
        );
        const productDetails = await getCompProductDetails(productPage, competitorUrl);
        // console.log({ productDetails });
        competitorName = productDetails.brand;
        competitorProductName = productDetails.title;
        competitorRevenue = productDetails.revenue;
        return {
          data: JSON.stringify(productDetails),
          companyId: companyId,
          status: "success",
          compKeyPrompt: "User Provided Competitor",
          searchKeyword: "User Provided Competitor",
          competitorRevenue: competitorRevenue,
          competitorProductName: competitorProductName,
          competitorName: competitorName,
          competitorProductAmazonURL: competitorProductAmazonURL,
          prospectProductName: prospectProductName,
          prospectRevenue: prospectRevenue || 0.0,
          prospectProductAmazonURL: prospectProductLink,
          prospectRevenueSource: prospectRevenueSource || "",
        };
      }

      if (!keywordResponse) {
        const description = prospectData.bulletPoints.value;
        searchData = {
          title: prospectData.productTitle.value,
          description: description,
        };
        if (searchData) {
          const options = {
            functionName: "fetchCompAmazonData",
            customTags: [
              `clientId:${clientId}`,
              `factoryType:compSearchKeyword`,
            ],
          };
          keywordResponse = await completionFactory(
            "compSearchKeyword",
            searchData,
            clientId,
            options
          );
          // console.log("KEY WORD FORM GPT:", keywordResponse);
          if (!keywordResponse || !keywordResponse.message || keywordResponse.message == "") {
            keywordResponse = "Competitor Keyword Not Found";
            console.log({keywordResponse});
            invalidKeyword = true;
             return {
               data: JSON.stringify({ status: "Invalid Keyword" }),
               companyId: companyId,
               status: "Invalid Keyword",
               compKeyPrompt: JSON.stringify(searchData),
               searchKeyword: "",
               competitorRevenue: 0.0,
               competitorProductName: "",
               competitorName: "",
               competitorProductAmazonURL: "",
               prospectProductName: prospectProductName,
               prospectRevenue: prospectRevenue || 0.0,
               prospectProductAmazonURL: prospectProductLink,
               prospectRevenueSource: prospectRevenueSource || "",
             };
          } else {
            // console.log("GPT KEY RES:", keywordResponse.response);
            usage[0].promptTokens += keywordResponse?.prompt_tokens;
            usage[0].completionTokens += keywordResponse?.completion_tokens;
            usage[0].totalTokens += keywordResponse?.total_tokens;
            usage[0].totalRuns += 1;
            keyword = keywordResponse?.message
              .trim()
              .replace(/[^\w\s-]/g, "")
              .replace(/\s+/g, "+")
              .replace(/^-+|-+$/g, "");
          }

          if (keyword === "Not+found") {
            invalidKeyword = true;
            // continue;
          }
        } else {
          console.log("No search data available for prospect product");
        }
      }
      // console.log({ keywordResponse, invalidKeyword });
      if (!invalidKeyword) {
        const baseSearchUrl = `${TARGET_URL}/s?k=${keyword}`;
        const { minMultiplier, maxMultiplier } = getMultipliers(prospectPrice);
        compSearchUrl =
          baseSearchUrl +
          `&&low-price=${prospectPrice * minMultiplier}&high-price=${
            prospectPrice * maxMultiplier
          }`;
        console.log("Getting competition data from range:", compSearchUrl);
        const htmlData = await getHtmlByProxy(compSearchUrl, clientId);
        console.log("With Revenue Range");

        compData = await getCompWithAmazonData({
          htmlData: htmlData,
          ogPrice: prospectPrice,
          ogCompany: amazonData.productData[0].brandName || company_name,
          ogRevenue: prospectRevenue || 0.0,
          ogAsin: ogAsin,
          clientId: clientId,
          TARGET_URL: TARGET_URL,
        });
        console.log("FILTERED URL COMP DATA:", compData);
        if (compData.data && compData.data.asin) {
          compData.data.prospectAsin = prospectData.asin;
          let competitorRevenue = 0;
          let competitorProductName = "";
          let competitorName = "";
          let competitorProductAmazonURL = `${TARGET_URL}/dp/${compData.data.asin}`;
          const productPage = await getHtmlByProxy(
            competitorProductAmazonURL,
            clientId,
            undefined,
            companyId
          );
          const productDetails = await getCompProductDetails(productPage, competitorProductAmazonURL);
          competitorName = productDetails.brand;
          competitorProductName = productDetails.title;
          competitorRevenue = compData.data.revenue;

          return {
            data: JSON.stringify(compData.data),
            companyId: companyId,
            status: compData.status,
            compKeyPrompt: JSON.stringify(searchData),
            searchKeyword: keyword,
            competitorRevenue: competitorRevenue,
            competitorProductName: competitorProductName,
            competitorName: competitorName,
            competitorProductAmazonURL: competitorProductAmazonURL,
            prospectProductName: prospectProductName,
            prospectRevenue: prospectRevenue || 0.0,
            prospectProductAmazonURL: prospectProductLink,
            prospectRevenueSource: prospectRevenueSource || "",
          };
        }
        console.log("With Base Url");
        compData = await getCompWithAmazonData({
          htmlData: await getHtmlByProxy(baseSearchUrl, clientId),
          ogPrice: prospectPrice,
          ogCompany: company_name,
          ogRevenue: prospectRevenue || 0.0,
          ogAsin: ogAsin,
          clientId: clientId,
        });
        console.log("FILTERED BASE URL COMP DATA:", compData);
        if (compData.data && compData.data.asin) {
          compData.data.prospectAsin = prospectData.asin;
          let competitorRevenue = 0;
          let competitorProductName = "";
          let competitorName = "";
          let competitorProductAmazonURL = `${TARGET_URL}/dp/${compData.data.asin}`;
          const productPage = await getHtmlByProxy(
            competitorProductAmazonURL,
            clientId
          );
          const productDetails = getCompProductDetails(productPage, competitorProductAmazonURL);
          competitorName = productDetails.brand;
          competitorProductName = productDetails.title;
          competitorRevenue = compData.data.revenue;

          return {
            data: JSON.stringify(compData.data),
            companyId: companyId,
            status: compData.status,
            compKeyPrompt: JSON.stringify(searchData),
            searchKeyword: keyword,
            competitorRevenue: competitorRevenue,
            competitorProductName: competitorProductName,
            competitorName: competitorName,
            competitorProductAmazonURL: competitorProductAmazonURL,
            prospectProductName: prospectProductName,
            prospectRevenue: prospectRevenue || 0.0,
            prospectProductAmazonURL: prospectProductLink,
            prospectRevenueSource: prospectRevenueSource || "",
          };
        }
      }
    } 
    if (invalidKeyword) {
      return {
        data: JSON.stringify({ status: "Invalid Keyword" }),
        companyId: companyId,
        status: "Invalid Keyword",
        compKeyPrompt: JSON.stringify(searchData),
        searchKeyword: keyword,
        competitorRevenue: 0.0,
        competitorProductName: "",
        competitorName: "",
        competitorProductAmazonURL: "",
        prospectProductName: prospectProductName,
        prospectRevenue: prospectRevenue || 0.0,
        prospectProductAmazonURL: prospectProductLink,
        prospectRevenueSource: prospectRevenueSource || "",
      };
    } else {
      return {
        data: JSON.stringify({
          status: "No Competition Found in the Specified range",
        }),
        companyId: companyId,
        status: "Unsuccessful due to `error`",
        compKeyPrompt: searchData ? JSON.stringify(searchData) : "",
        searchKeyword: keyword ? keyword : "",
        competitorRevenue: 0.0,
        competitorProductName: "",
        competitorName: "",
        competitorProductAmazonURL: "",
        prospectProductName: prospectProductName,
        prospectRevenue: prospectRevenue || 0.0,
        prospectProductAmazonURL: prospectProductLink,
        prospectRevenueSource: prospectRevenueSource || "",
      };
    }
  } catch (e) {
    console.error("Error Stack:", e.stack);
    console.log("Error in fetching comp data from amazon data: " + e);
  }
}

module.exports = fetchCompAmazonData;