const prisma = require("../database/prisma/getPrismaClient");

/**
 * Extract seller ID from Amazon seller URL
 * Pattern: https://www.amazon.de/-/en/s?ie=UTF8&marketplaceID=A1PA6795UKMFR9&me=A70TN3VD8H6ZZ
 * The seller ID is the value after "me=" parameter
 */
function extractSellerIdFromUrl(sellerUrl) {
    if (!sellerUrl || typeof sellerUrl !== 'string') {
        return null;
    }

    try {
        // Method 1: Extract from "me=" parameter
        const meMatch = sellerUrl.match(/[?&]me=([^&]+)/);
        if (meMatch && meMatch[1]) {
            return meMatch[1].trim();
        }

        // Method 2: Extract from "seller=" parameter (alternative format)
        const sellerMatch = sellerUrl.match(/[?&]seller=([^&]+)/);
        if (sellerMatch && sellerMatch[1]) {
            return sellerMatch[1].trim();
        }

        // Method 3: Extract from path if it contains seller ID pattern
        const pathMatch = sellerUrl.match(/\/([A-Z0-9]{10,15})\/?$/);
        if (pathMatch && pathMatch[1]) {
            return pathMatch[1].trim();
        }

        return null;
    } catch (error) {
        console.error("Error extracting seller ID from URL:", error);
        return null;
    }
}

/**
 * Find records that have sellerURL but are missing sellerID
 */
async function findRecordsWithUrlButNoSellerId() {
    try {
        const records = await prisma.jeffDataAnalysis.findMany({
            where: {
                AND: [
                    {
                        sellerUrl: {
                            not: null,
                            not: ""
                        }
                    },
                    {
                        OR: [
                            { sellerId: null },
                            { sellerId: "" }
                        ]
                    }
                ]
            },
            select: {
                id: true,
                product_slug: true,
                sellerId: true,
                sellerUrl: true,
                createdAt: true
            }
        });

        return { success: true, records, count: records.length };
    } catch (error) {
        console.error("Error finding records with URL but no sellerId:", error);
        return { success: false, error: error.message };
    }
}

/**
 * Update sellerID for records using extracted value from sellerURL
 */
async function updateSellerIdFromUrl(recordId, extractedSellerId) {
    try {
        const result = await prisma.jeffDataAnalysis.update({
            where: { id: recordId },
            data: { sellerId: extractedSellerId }
        });

        return { success: true, record: result };
    } catch (error) {
        console.error(`Error updating record ${recordId}:`, error);
        return { success: false, error: error.message };
    }
}

/**
 * Main function to extract and update sellerIDs from URLs
 */
async function extractAndUpdateSellerIds() {
    try {
        console.log("🔍 Finding records with sellerURL but missing sellerID...");

        const findResult = await findRecordsWithUrlButNoSellerId();

        if (!findResult.success) {
            console.error("❌ Error finding records:", findResult.error);
            return;
        }

        if (findResult.count === 0) {
            console.log("✅ No records found with sellerURL but missing sellerID!");
            return;
        }

        console.log(`📊 Found ${findResult.count} records with sellerURL but missing sellerID:`);

        let updatedCount = 0;
        let failedCount = 0;
        let skippedCount = 0;

        for (const record of findResult.records) {
            console.log(`\n🔗 Processing record ID: ${record.id}`);
            console.log(`   Product Slug: ${record.product_slug}`);
            console.log(`   Current SellerID: "${record.sellerId}"`);
            console.log(`   SellerURL: ${record.sellerUrl}`);

            const extractedSellerId = extractSellerIdFromUrl(record.sellerUrl);

            if (!extractedSellerId) {
                console.log(`   ⚠️  Could not extract sellerID from URL - skipping`);
                skippedCount++;
                continue;
            }

            console.log(`   ✅ Extracted SellerID: ${extractedSellerId}`);

            // Update the record with the extracted sellerID
            const updateResult = await updateSellerIdFromUrl(record.id, extractedSellerId);

            if (updateResult.success) {
                console.log(`   ✅ Successfully updated record ${record.id}`);
                updatedCount++;
            } else {
                console.log(`   ❌ Failed to update record ${record.id}: ${updateResult.error}`);
                failedCount++;
            }
        }

        // Summary
        console.log(`\n🎉 Extraction and update completed!`);
        console.log(`📊 Summary:`);
        console.log(`   - Total records processed: ${findResult.count}`);
        console.log(`   - Successfully updated: ${updatedCount}`);
        console.log(`   - Failed to update: ${failedCount}`);
        console.log(`   - Skipped (no sellerID found): ${skippedCount}`);

    } catch (error) {
        console.error("❌ Error in extractAndUpdateSellerIds:", error);
    }
}

/**
 * Test function to test sellerID extraction from a sample URL
 */
async function testSellerIdExtraction() {
    console.log("🧪 Testing sellerID extraction...");

    const testUrls = [
        "https://www.amazon.de/-/en/s?ie=UTF8&marketplaceID=A1PA6795UKMFR9&me=A70TN3VD8H6ZZ",
        "https://www.amazon.com/s?me=ABC123DEF4",
        "https://www.amazon.co.uk/s?seller=XYZ789GHI0",
        "https://www.amazon.com/s?ie=UTF8&seller=TEST123456",
        "https://www.amazon.de/s?ie=UTF8&me=INVALID"
    ];

    testUrls.forEach(url => {
        const sellerId = extractSellerIdFromUrl(url);
        console.log(`URL: ${url}`);
        console.log(`Extracted SellerID: ${sellerId || 'NOT FOUND'}`);
        console.log('---');
    });
}

/**
 * Main execution function
 */
async function main() {
    try {
        console.log("🚀 Starting SellerID extraction from URLs...");

        // Check if test mode is requested
        const isTestMode = process.argv.includes('--test');

        if (isTestMode) {
            console.log("🧪 Running in TEST MODE - testing URL extraction only");
            await testSellerIdExtraction();
        } else {
            await extractAndUpdateSellerIds();
        }

        console.log("✅ SellerID extraction script completed successfully!");
    } catch (error) {
        console.error("❌ SellerID extraction script failed:", error);
        process.exit(1);
    } finally {
        await prisma.$disconnect();
    }
}

// Run the script if called directly
if (require.main === module) {
    main();
}

module.exports = {
    extractSellerIdFromUrl,
    findRecordsWithUrlButNoSellerId,
    updateSellerIdFromUrl,
    extractAndUpdateSellerIds,
    testSellerIdExtraction,
    main
};
