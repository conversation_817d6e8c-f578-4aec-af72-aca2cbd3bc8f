const prisma = require("../database/prisma/getPrismaClient");
const { getMarketplaceFromUrl, upsertJeffDataAnalysis, extractSellerIdFromUrl } = require("../utils/jeffDataAnalysisUtils");

/**
 * Build JeffDataAnalysis record data without database calls for performance
 */
function buildJeffDataRecord(outputData, amazonAudit, productData, store, prospectDetails, sellerDetails, auditMailData, competitorDetails, auditReport) {
    try {
        // Extract the first product from the array if it exists
        const firstProduct = Array.isArray(productData) ? productData[0] : productData;

        const sellerUrl = sellerDetails?.['Seller Storefront Link'] || "";
        const marketplace = getMarketplaceFromUrl(sellerUrl);

        // Extract seller ID directly from seller URL (more efficient than checking multiple fields)
        let sellerId = extractSellerIdFromUrl(sellerUrl);

        // STRICT VALIDATION: Reject any record without a valid seller ID
        if (!sellerId || sellerId.trim() === "" || !/^[A-Za-z0-9]+$/.test(sellerId.trim())) {
            console.log(`❌ No valid seller ID found in URL: ${sellerUrl}`);
            return null; // Return null to skip this record
        }

        // Clean string utility function
        function cleanString(str, maxLength = null) {
            if (!str) return "";
            let cleaned = str.replace(/\s+/g, ' ').trim();
            if (maxLength && cleaned.length > maxLength) {
                cleaned = cleaned.substring(0, maxLength);
            }
            return cleaned;
        }

        const jeffDataRecord = {
            // Seller information
            sellerId: sellerId,
            marketplace: marketplace,
            sellerUrl: cleanString(sellerUrl),

            // Basic product information
            url: cleanString(firstProduct?.url || ""), // Required field in schema
            brand_name: cleanString(firstProduct?.company_name || outputData.companyName, 255),
            product_title: cleanString(firstProduct?.title || prospectDetails?.productName || ""),
            description: cleanString(firstProduct?.description || ""),
            price: firstProduct?.price ? Math.min(parseFloat(firstProduct.price), 99999999.99) : null,

            // Ratings & reviews
            rating: firstProduct?.rating?.rating ? parseFloat(firstProduct.rating.rating) : null,
            total_reviews: firstProduct?.review?.totalReviewCountInt || null,
            review_category: cleanString(firstProduct?.review?.reviewsCategory || "", 100),
            star_5_count: firstProduct?.review?.reviewPerStar?.[0]?.['5starReview'] || 0,
            star_4_count: firstProduct?.review?.reviewPerStar?.[1]?.['4starReview'] || 0,
            star_3_count: firstProduct?.review?.reviewPerStar?.[2]?.['3starReview'] || 0,
            star_2_count: firstProduct?.review?.reviewPerStar?.[3]?.['2starReview'] || 0,
            star_1_count: firstProduct?.review?.reviewPerStar?.[4]?.['1starReview'] || 0,
            sales_count: firstProduct?.sales || null,

            // Media
            main_image_url: cleanString(firstProduct?.productMainImage || ""),
            image_count: firstProduct?.images?.noOfImages || null,
            video_count: firstProduct?.images?.noOfVideos || null,

            // Metadata
            title_char_count: firstProduct?.title ? firstProduct.title.length : null,
            title_under_150_chars: firstProduct?.title ? firstProduct.title.length <= 150 : null,
            out_of_stock: firstProduct?.outOfStock || false,

            // Amazon features
            aplus_content_present: !!firstProduct?.AplusContent && Object.keys(firstProduct.AplusContent).length > 0,
            premium_aplus_present: false, // This would need specific detection logic
            brand_story_present: !!firstProduct?.AplusContent?.brandStory,
            storefront_present: store?.storefront_present || false,
            storefront_url: cleanString(store?.store_front_url || ""),

            // Revenue and qualification
            prospect_revenue: parseFloat(prospectDetails?.revenue || 0),
            revenue_difference_monthly: parseFloat(outputData.revenueDifference || 0),
            revenue_difference_yearly: parseFloat((outputData.revenueDifference || 0) * 12),
            revenue_source: cleanString(prospectDetails?.revenueSource || "", 255),

            // Company and job information
            company_name: cleanString(outputData.companyName || "", 255),
            company_name_humanized: cleanString(prospectDetails?.humanizedProspectName || ""),
            campaign_name: cleanString(outputData.campaignName || "", 255),

            // Product details
            product_slug: cleanString(outputData.productSlug || "", 255),
            product_amazon_url: cleanString(prospectDetails?.productAmazonURL || ""),
            product_title_humanized: cleanString(prospectDetails?.humanizedProspectProductTitle || ""),
            product_asin: cleanString(firstProduct?.asin || "", 50),

            // Search and category
            branded_keyword: cleanString(auditMailData?.brandedKeyword || "", 255),
            non_branded_keyword: cleanString(auditMailData?.nonBrandedKeyword || "", 255),

            // Competitor information
            competitor_product_url: cleanString(competitorDetails?.productAmazonURL || ""),
            competitor_asin: cleanString(competitorDetails?.asin || "", 50),
            competitor_product_title: cleanString(competitorDetails?.productName || ""),
            competitor_product_title_humanized: cleanString(competitorDetails?.humanizedCompProductTitle || ""),
            competitor_brand_name: cleanString(competitorDetails?.name || "", 255),
            competitor_brand_name_humanized: cleanString(competitorDetails?.humanizedCompCompanyName || ""),
            competitor_revenue: parseFloat(competitorDetails?.revenue || 0),
            competitor_annual_revenue: parseFloat((competitorDetails?.revenue || 0) * 12),

            // Audit analysis fields
            number_of_optimizations: Object.keys(auditReport || {}).length,
            main_image_optimization: cleanString(auditMailData?.mainImageOptimisationText || ""),
            data_point_1: cleanString(auditMailData?.Data_Point_1 || ""),
            data_point_2: cleanString(auditMailData?.Data_Point_2 || ""),
            data_point_3: cleanString(auditMailData?.Data_Point_3 || ""),
            pain_point_1: cleanString(auditMailData?.Pain_Point_1 || ""),
            pain_point_2: cleanString(auditMailData?.Pain_Point_2 || ""),
            pain_point_3: cleanString(auditMailData?.Pain_Point_3 || ""),
            top_improvement_1: cleanString(auditMailData?.Top_Improvement_1 || ""),
            top_improvement_2: cleanString(auditMailData?.Top_Improvement_2 || ""),
            top_improvement_3: cleanString(auditMailData?.Top_Improvement_3 || ""),

            // JSON fields for complex data
            bullet_points: firstProduct?.bulletPoints || {},
            categories_and_ranks: firstProduct?.categoryAndRank || {},
            secondary_images: firstProduct?.secondaryImages || {},
            brand_story_images: firstProduct?.AplusContent?.brandStory?.finalBrandStoryArray || {},

            // Flattened category and rank fields
            category_1: cleanString(firstProduct?.categoryAndRank?.[0]?.category || "", 255),
            rank_1: firstProduct?.categoryAndRank?.[0]?.rank || null,
            category_2: cleanString(firstProduct?.categoryAndRank?.[1]?.category || "", 255),
            rank_2: firstProduct?.categoryAndRank?.[1]?.rank || null,

            // Flattened bullet point fields
            bullet_point_1: cleanString(firstProduct?.bulletPoints?.Points?.[0]?.value || ""),
            bullet_point_1_chars: firstProduct?.bulletPoints?.Points?.[0]?.NumberChars || null,
            bullet_point_2: cleanString(firstProduct?.bulletPoints?.Points?.[1]?.value || ""),
            bullet_point_2_chars: firstProduct?.bulletPoints?.Points?.[1]?.NumberChars || null,
            bullet_point_3: cleanString(firstProduct?.bulletPoints?.Points?.[2]?.value || ""),
            bullet_point_3_chars: firstProduct?.bulletPoints?.Points?.[2]?.NumberChars || null,
            bullet_point_4: cleanString(firstProduct?.bulletPoints?.Points?.[3]?.value || ""),
            bullet_point_4_chars: firstProduct?.bulletPoints?.Points?.[3]?.NumberChars || null,
            bullet_points_all_caps: firstProduct?.bulletPoints?.isItAllCaps || false,

            createdAt: outputData.createdAt,
        };

        return jeffDataRecord;
    } catch (error) {
        console.error("Error building JeffDataAnalysis record:", error);
        return null;
    }
}

/**
 * Utility function to add a single JeffDataAnalysis record
 * Can be used for testing, individual inserts, or bulk operations
 */
async function addJeffDataAnalysisRecord(outputData, amazonAudit, productData, store, prospectDetails, sellerDetails, auditMailData, competitorDetails, auditReport) {
    try {
        // Use the shared utility function
        const result = await upsertJeffDataAnalysis(
            outputData,
            amazonAudit,
            productData,
            store,
            prospectDetails,
            sellerDetails,
            auditMailData,
            competitorDetails,
            auditReport
        );

        return result;
    } catch (error) {
        return { success: false, error: error.message };
    }
}

/**
 * Test function to add a single record for testing purposes
 */
async function testSingleRecord() {
    try {
        console.log("🧪 Testing single record insertion...");

        // Get the first eligible record
        // const testRecord = await prisma.outputData.findFirst({
        //     where: {
        //         AND: [
        //             {
        //                 createdAt: {
        //                     gte: new Date('2025-07-15')
        //                 }
        //             },
        //             {
        //                 sellerDetails: {
        //                     not: "{}"
        //                 }
        //             },
        //             {
        //                 amazonAudit: {
        //                     not: "{}"
        //                 }
        //             }
        //         ]
        //     },
        //     include: {
        //         job: true
        //     }
        // });

        // if (!testRecord) {
        //     console.log("❌ No test record found");
        //     return;
        // }
        const id = 740053;

        console.log(`📝 Testing with record ID: ${id}`);

        // Parse the JSON fields (handle both string and object formats)
        const testRecord = await prisma.outputData.findUnique({
            where: { id: id }
        });

        const sellerDetails = typeof testRecord.sellerDetails === 'string'
            ? JSON.parse(testRecord.sellerDetails || "{}")
            : (testRecord.sellerDetails || {});

        const amazonAudit = typeof testRecord.amazonAudit === 'string'
            ? JSON.parse(testRecord.amazonAudit || "{}")
            : (testRecord.amazonAudit || {});

        const productData = amazonAudit?.productData || {};
        const store = amazonAudit?.store || {};
        const prospectDetails = testRecord?.prospectDetails || {};
        const auditMailData = testRecord?.auditMailData || {};
        const competitorDetails = testRecord?.competitorDetails || {};
        const auditReport = testRecord?.auditReport || {};

        // console.log({ productData, store, prospectDetails, sellerDetails, auditMailData, competitorDetails, auditReport });

        // Test the add function
        const result = await addJeffDataAnalysisRecord(
            testRecord, amazonAudit, productData, store,
            prospectDetails, sellerDetails, auditMailData,
            competitorDetails, auditReport
        );


        if (result.success) {
            console.log("✅ Test record inserted successfully!");
            console.log(`📊 Record ID: ${result.record.id}`);

            // Clean up the test record
            // await prisma.jeffDataAnalysis.delete({
            //     where: { id: result.record.id }
            // });
            // console.log("🧹 Test record cleaned up");
        } else {
            console.log("❌ Test failed:", result.error);
        }

    } catch (error) {
        console.error("❌ Test function error:", error);
    }
}

/**
 * Seeder script to populate JeffDataAnalysis table from OutputData
 * Filters:
 * - Only data with ID > 680000 (sequential filter)
 * - Only rows with sellerID and amazonData
 * - Maintains history like OutputData table
 */

async function seedJeffDataAnalysis() {
    try {
        console.log("🚀 Starting JeffDataAnalysis seeding...");

        // First check if the JeffDataAnalysis table exists
        try {
            await prisma.jeffDataAnalysis.findFirst();
            console.log("✅ JeffDataAnalysis table exists");
        } catch (error) {
            console.error("❌ JeffDataAnalysis table does not exist. Please run the migration first:");
            console.error("   npx prisma migrate dev --name add_jeff_data_analysis_table");
            console.error("   OR");
            console.error("   npx prisma db push");
            throw new Error("Database table missing");
        }

        // Define the cutoff ID and date (all records after ID 680000 AND after July 15, 2024)
        const cutoffId = 680000;
        const cutoffDate = new Date('2025-07-15');
        console.log(`📅 Filtering data from date > ${cutoffDate.toISOString().split('T')[0]}`);

        // Get count of eligible records for progress tracking
        const totalCount = await prisma.outputData.count({
            where: {
                AND: [
                    {
                        createdAt: {
                            gte: cutoffDate
                        }
                    },
                    {
                        // Check if sellerDetails is not empty JSON
                        sellerDetails: {
                            not: "{}"
                        }
                    },
                    {
                        // Check if amazonAudit exists and is not empty
                        amazonAudit: {
                            not: "{}"
                        }
                    }
                ]
            }
        });

        console.log(`📊 Found ${totalCount} eligible records to seed`);

        if (totalCount === 0) {
            console.log("⚠️  No eligible records found. Exiting...");
            return;
        }

        // Process records in batches to avoid memory issues
        const batchSize = 3000; // Increased for better performance
        let processed = 0;
        let seeded = 0;
        let skipped = 0;

        // Performance tracking
        const startTime = Date.now();
        let lastProgressTime = startTime;

        for (let skip = 0; skip < totalCount; skip += batchSize) {
            console.log(`📦 Processing batch ${Math.floor(skip / batchSize) + 1}/${Math.ceil(totalCount / batchSize)}`);

            const outputDataBatch = await prisma.outputData.findMany({
                where: {
                    AND: [
                        {
                            createdAt: {
                                gte: cutoffDate
                            }
                        },
                        {
                            // Check if sellerDetails is not empty JSON
                            sellerDetails: {
                                not: "{}"
                            }
                        },
                        {
                            // Check if amazonAudit exists and is not empty
                            amazonAudit: {
                                not: "{}"
                            }
                        }
                    ]
                },
                skip: skip,
                take: batchSize,
                include: {
                    job: true
                }
            });

            const jeffDataRecords = [];

            // Process records in parallel for better performance
            const processingPromises = outputDataBatch.map(async (outputData) => {
                try {
                    // Extract data with safe parsing
                    const amazonAudit = typeof outputData.amazonAudit === 'string'
                        ? JSON.parse(outputData.amazonAudit)
                        : outputData.amazonAudit;

                    const auditReport = typeof outputData.auditReport === 'string'
                        ? JSON.parse(outputData.auditReport)
                        : outputData.auditReport;

                    const prospectDetails = typeof outputData.prospectDetails === 'string'
                        ? JSON.parse(outputData.prospectDetails)
                        : outputData.prospectDetails;

                    const competitorDetails = typeof outputData.competitorDetails === 'string'
                        ? JSON.parse(outputData.competitorDetails)
                        : outputData.competitorDetails;

                    const sellerDetails = typeof outputData.sellerDetails === 'string'
                        ? JSON.parse(outputData.sellerDetails)
                        : outputData.sellerDetails;

                    const auditMailData = typeof outputData.auditMailData === 'string'
                        ? JSON.parse(outputData.auditMailData)
                        : outputData.auditMailData;

                    // Extract seller ID directly from seller URL (more efficient)
                    const sellerUrl = sellerDetails?.['Seller Storefront Link'] || "";
                    const sellerId = extractSellerIdFromUrl(sellerUrl);

                    // Skip if no seller ID found or if amazonAudit is empty
                    if (!sellerId || !amazonAudit || Object.keys(amazonAudit).length === 0) {
                        return { success: false, reason: 'no_seller_id_or_amazon_audit' };
                    }

                    // Extract product data from amazonAudit
                    const productData = amazonAudit?.productData?.[0] || {};
                    const store = amazonAudit?.store || {};

                    // Build the record data directly instead of calling the utility function
                    const jeffDataRecord = buildJeffDataRecord(
                        outputData, amazonAudit, productData, store,
                        prospectDetails, sellerDetails, auditMailData,
                        competitorDetails, auditReport
                    );

                    if (jeffDataRecord) {
                        return { success: true, record: jeffDataRecord };
                    } else {
                        return { success: false, reason: 'invalid_record' };
                    }

                } catch (error) {
                    console.error(`❌ Error processing record ${outputData.id}:`, error.message);
                    return { success: false, reason: 'error', error: error.message };
                }
            });

            // Wait for all processing to complete
            const processingResults = await Promise.all(processingPromises);

            // Collect successful records and count results
            for (const result of processingResults) {
                if (result.success) {
                    jeffDataRecords.push(result.record);
                    seeded++;
                } else {
                    skipped++;
                }
                processed++;
            }

            // Bulk insert the records for maximum performance
            if (jeffDataRecords.length > 0) {
                try {
                    await prisma.jeffDataAnalysis.createMany({
                        data: jeffDataRecords,
                        skipDuplicates: true
                    });
                    console.log(`✅ Bulk inserted ${jeffDataRecords.length} records in this batch`);
                } catch (error) {
                    console.error(`❌ Bulk insert error:`, error.message);
                    console.log(`🔄 Falling back to individual inserts...`);

                    // Fallback to individual inserts if bulk fails
                    let individualSuccess = 0;
                    for (const record of jeffDataRecords) {
                        try {
                            await prisma.jeffDataAnalysis.create({ data: record });
                            individualSuccess++;
                        } catch (insertError) {
                            console.error(`❌ Individual insert failed:`, insertError.message);
                        }
                    }
                    console.log(`✅ Individual inserts completed: ${individualSuccess}/${jeffDataRecords.length} successful`);
                }
            }

            // Progress update with performance metrics
            const currentTime = Date.now();
            const batchTime = currentTime - lastProgressTime;
            const totalTime = currentTime - startTime;
            const avgTimePerRecord = processed > 0 ? totalTime / processed : 0;
            const estimatedTimeRemaining = (totalCount - processed) * avgTimePerRecord;

            console.log(`📈 Progress: ${processed}/${totalCount} processed, ${seeded} seeded, ${skipped} skipped`);
            console.log(`⏱️  Batch time: ${batchTime}ms, Total time: ${Math.round(totalTime / 1000)}s, ETA: ${Math.round(estimatedTimeRemaining / 1000)}s`);
            console.log(`🚀 Speed: ${Math.round(processed / (totalTime / 1000))} records/sec`);

            // Memory cleanup and database throttling
            if (jeffDataRecords.length > 0) {
                // Clear the array to free memory
                jeffDataRecords.length = 0;

                // Small delay to prevent overwhelming the database
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            lastProgressTime = currentTime;
        }

        console.log(`🎉 Seeding completed!`);
        console.log(`📊 Summary:`);
        console.log(`   - Total processed: ${processed}`);
        console.log(`   - Successfully seeded: ${seeded}`);
        console.log(`   - Skipped: ${skipped}`);
        console.log(`   - Success rate: ${((seeded / processed) * 100).toFixed(2)}%`);

    } catch (error) {
        console.error("❌ Error in seeding JeffDataAnalysis:", error);
        throw error;
    }
}

// Function to clear existing data (optional)
async function clearJeffDataAnalysis() {
    try {
        console.log("🧹 Clearing existing JeffDataAnalysis data...");
        const deletedCount = await prisma.jeffDataAnalysis.deleteMany({});
        console.log(`✅ Deleted ${deletedCount.count} existing records`);
    } catch (error) {
        console.error("❌ Error clearing JeffDataAnalysis:", error);
        throw error;
    }
}

// Main execution function
async function main() {
    try {
        console.log("🔄 Starting JeffDataAnalysis seeder...");

        // Check if test mode is requested
        const isTestMode = process.argv.includes('--test');

        if (isTestMode) {
            console.log("🧪 Running in TEST MODE - single record only");
            await testSingleRecord();
        } else {
            // Uncomment the line below if you want to clear existing data first
            // await clearJeffDataAnalysis();

            await seedJeffDataAnalysis();
        }

        console.log("✅ JeffDataAnalysis seeder completed successfully!");
    } catch (error) {
        console.error("❌ JeffDataAnalysis seeder failed:", error);
        process.exit(1);
    } finally {
        await prisma.$disconnect();
    }
}

// Run the script if called directly
if (require.main === module) {
    main();
}

module.exports = {
    seedJeffDataAnalysis,
    clearJeffDataAnalysis,
    addJeffDataAnalysisRecord,
    testSingleRecord,
    main
};
