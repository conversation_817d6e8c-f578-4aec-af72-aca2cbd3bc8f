const cron = require('node-cron');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Import the working seeder functions
const {
    transformAmazonDataToProduct,
} = require('../services/amazonDataSBSeeder/amazonProductDataToProductSeeder');

// Import the config functions
const {
    targetDbConfig,
    checkTargetTable,
    validateProductData,
    sanitizeProductData,
} = require('../services/amazonDataSBSeeder/amazonProductDataToProductConfig');

// Helper function to check if storeFrontURL contains a valid seller ID
function hasValidSellerInfo(storeFrontURL) {
    if (!storeFrontURL || typeof storeFrontURL !== 'string') {
        return false;
    }

    try {
        const url = new URL(storeFrontURL.startsWith('http') ? storeFrontURL : `https://${storeFrontURL}`);
        const sellerParam = url.searchParams.get('seller');
        const meParam = url.searchParams.get('me');

        return !!(sellerParam || meParam);
    } catch (error) {
        return false;
    }
}

// Migration configuration
const migrationConfig = {
    batchSize: parseInt(process.env.MIGRATION_BATCH_SIZE) || 50,
    // Always insert new rows - no skipping or updating
    skipExisting: false,
    updateExisting: false,
};

// Helper function to create target database connection
async function getTargetDbConnection() {
    const { Client } = require('pg');

    console.log(`🔌 Creating connection to: ${targetDbConfig.host}:${targetDbConfig.port}/${targetDbConfig.database}`);

    const client = new Client({
        host: targetDbConfig.host,
        port: targetDbConfig.port,
        database: targetDbConfig.database,
        user: targetDbConfig.username,
        password: targetDbConfig.password,
        ssl: targetDbConfig.ssl,
        connectionTimeoutMillis: targetDbConfig.connectionTimeoutMillis,
        idleTimeoutMillis: targetDbConfig.idleTimeoutMillis,
    });

    try {
        await client.connect();
        console.log('✅ Successfully connected to target database');
        return client;
    } catch (error) {
        console.error('❌ Failed to connect to target database:', error.message);
        throw error;
    }
}

// Main sync function
async function syncAmazonProductData() {
    let targetDb = null;

    try {
        console.log('🔄 Starting AmazonProductData sync job...');
        console.log(`⏰ Started at: ${new Date().toISOString()}`);

        // Check target table
        console.log('🔍 Checking target table...');
        const tableExists = await checkTargetTable();
        if (!tableExists) {
            throw new Error('Target table "products" does not exist');
        }

        // Get target database connection
        targetDb = await getTargetDbConnection();

        // Get records that need syncing (not pushed to SB) and have seller information
        const amazonProductDataList = await prisma.amazonProductData.findMany({
            where: {
                pushedToSB: false,
                status: "completed",
                company: {
                    storeFrontURL: {
                        not: null,
                        not: ""
                    }
                }
            },
            include: {
                company: true
            },
            orderBy: {
                updatedAt: 'desc'
            }
        });

        // Filter out records that don't have valid seller information
        const validRecords = amazonProductDataList.filter(record =>
            hasValidSellerInfo(record.company?.storeFrontURL)
        );

        console.log(`📊 Found ${amazonProductDataList.length} total records, ${validRecords.length} with valid seller information`);

        if (validRecords.length === 0) {
            console.log('✅ No records with valid seller information to sync.');
            return;
        }

        let successCount = 0;
        let errorCount = 0;
        let validationErrorCount = 0;

        // Process records in batches
        const batchSize = migrationConfig.batchSize;
        for (let i = 0; i < validRecords.length; i += batchSize) {
            const batch = validRecords.slice(i, i + batchSize);

            console.log(`📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(validRecords.length / batchSize)}`);

            for (const amazonProductData of batch) {
                try {
                    // Transform the data
                    const transformedProduct = await transformAmazonDataToProduct(amazonProductData);

                    // Check if seller information is missing
                    if (!transformedProduct.sellerId || !transformedProduct.sellerUrl ||
                        transformedProduct.sellerId === '' || transformedProduct.sellerUrl === '') {
                        console.log(`⏭️ Skipping AmazonProductData ID ${amazonProductData.id} - Missing seller information (sellerId: ${transformedProduct.sellerId}, sellerUrl: ${transformedProduct.sellerUrl})`);
                        validationErrorCount++;
                        continue;
                    }

                    // Validate the transformed data
                    const validationErrors = validateProductData(transformedProduct);
                    if (validationErrors.length > 0) {
                        console.warn(`⚠️ Validation errors for AmazonProductData ID ${amazonProductData.id}:`, validationErrors);
                        validationErrorCount++;

                        // Skip records with critical validation errors
                        const criticalErrors = validationErrors.filter(error =>
                            error.includes('Missing required field') || error.includes('Invalid URL format')
                        );
                        if (criticalErrors.length > 0) {
                            console.log(`⏭️ Skipping AmazonProductData ID ${amazonProductData.id} due to critical validation errors`);
                            continue;
                        }
                    }

                    // Always insert new row - track history of all product data changes
                    console.log(`📝 Inserting new row for Product with URL ${transformedProduct.url}`);

                    // Insert into target database
                    const insertQuery = `
                        INSERT INTO products (
                          "sellerId", marketplace, "sellerUrl", url, brand_name, product_title, description, price,
                          rating, total_reviews, review_category, star_5_count, star_4_count,
                          star_3_count, star_2_count, star_1_count, sales_count,
                          main_image_url, image_count, video_count,
                          title_char_count, title_under_150_chars, out_of_stock,
                          aplus_content_present, premium_aplus_present, brand_story_present,
                          storefront_present, storefront_url,
                          bullet_points, categories_and_ranks, secondary_images, brand_story_images,
                          full_json_data, "createdAt", "updatedAt"
                        ) VALUES (
                          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35
                        ) RETURNING id
                    `;

                    const values = [
                        transformedProduct.sellerId,
                        transformedProduct.marketplace,
                        transformedProduct.sellerUrl,
                        transformedProduct.url,
                        transformedProduct.brand_name,
                        transformedProduct.product_title,
                        transformedProduct.description,
                        transformedProduct.price,
                        transformedProduct.rating,
                        transformedProduct.total_reviews,
                        transformedProduct.review_category,
                        transformedProduct.star_5_count,
                        transformedProduct.star_4_count,
                        transformedProduct.star_3_count,
                        transformedProduct.star_2_count,
                        transformedProduct.star_1_count,
                        transformedProduct.sales_count,
                        transformedProduct.main_image_url,
                        transformedProduct.image_count,
                        transformedProduct.video_count,
                        transformedProduct.title_char_count,
                        transformedProduct.title_under_150_chars,
                        transformedProduct.out_of_stock,
                        transformedProduct.aplus_content_present,
                        transformedProduct.premium_aplus_present,
                        transformedProduct.brand_story_present,
                        transformedProduct.storefront_present,
                        transformedProduct.storefront_url,
                        transformedProduct.bullet_points,
                        transformedProduct.categories_and_ranks,
                        transformedProduct.secondary_images,
                        transformedProduct.brand_story_images,
                        transformedProduct.full_json_data,
                        transformedProduct.createdAt,
                        transformedProduct.updatedAt
                    ];

                    const result = await targetDb.query(insertQuery, values);

                    // Mark as pushed to SB
                    await prisma.amazonProductData.update({
                        where: { id: amazonProductData.id },
                        data: { pushedToSB: true }
                    });

                    console.log(`✅ Successfully migrated AmazonProductData ID ${amazonProductData.id} to Product ID ${result.rows[0].id}`);
                    successCount++;

                } catch (error) {
                    console.error(`❌ Error migrating AmazonProductData ID ${amazonProductData.id}:`, error.message);
                    errorCount++;
                }
            }
        }

        console.log('\n📈 === Sync Summary ===');
        console.log(`📊 Total records found: ${amazonProductDataList.length}`);
        console.log(`📊 Records with valid seller info: ${validRecords.length}`);
        console.log(`✅ Successfully synced: ${successCount}`);
        console.log(`⚠️ Validation errors: ${validationErrorCount}`);
        console.log(`❌ Database errors: ${errorCount}`);
        console.log(`⏰ Completed at: ${new Date().toISOString()}`);
        console.log('========================\n');

    } catch (error) {
        console.error('❌ Sync job failed:', error);
        throw error;
    } finally {
        // Clean up connections
        if (targetDb) {
            await targetDb.end();
        }
    }
}

// Initialize the cron job
function initializeSyncJob() {
    console.log('🚀 Initializing AmazonProductData sync cron job...');
    console.log('⏰ Schedule: Every 6 hours (0 */6 * * *)');

    // Schedule the job to run every 6 hours
    const job = cron.schedule('0 */6 * * *', async () => {
        try {
            await syncAmazonProductData();
        } catch (error) {
            console.error('❌ Cron job failed:', error);
        }
    }, {
        scheduled: true,
        timezone: "UTC",
        runOnInit: true
    });

    console.log('✅ AmazonProductData sync cron job initialized successfully');

    return job;
}

// Export functions
module.exports = {
    syncAmazonProductData,
    initializeSyncJob
};

// If this file is run directly, start the cron job
if (require.main === module) {
    console.log('🔄 Starting AmazonProductData sync worker...');

    // Initialize cron job (will run immediately due to runOnInit: true)
    const job = initializeSyncJob();

    // Handle graceful shutdown
    process.on('SIGINT', () => {
        console.log('🛑 Shutting down AmazonProductData sync worker...');
        job.stop();
        prisma.$disconnect();
        process.exit(0);
    });

    process.on('SIGTERM', () => {
        console.log('🛑 Shutting down AmazonProductData sync worker...');
        job.stop();
        prisma.$disconnect();
        process.exit(0);
    });
} 