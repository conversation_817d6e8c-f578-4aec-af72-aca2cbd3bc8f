/*
  Warnings:

  - You are about to drop the column `aplusContentPresentRaw` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `brandStoryPresentRaw` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `bulletPointsRaw` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `imageCountRaw` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `outOfStockRaw` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `premiumAplusPresentRaw` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `priceCurrency` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `priceRaw` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `productTitleRaw` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `salesCountRaw` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `secondaryImagesRaw` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `storefrontPresentRaw` on the `JeffDataAnalysis` table. All the data in the column will be lost.
  - You are about to drop the column `videoCountRaw` on the `JeffDataAnalysis` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "JeffDataAnalysis" DROP COLUMN "aplusContentPresentRaw",
DROP COLUMN "brandStoryPresentRaw",
DROP COLUMN "bulletPointsRaw",
DROP COLUMN "imageCountRaw",
DROP COLUMN "outOfStockRaw",
DROP COLUMN "premiumAplusPresentRaw",
DROP COLUMN "priceCurrency",
DROP COLUMN "priceRaw",
DROP COLUMN "productTitleRaw",
DROP COLUMN "salesCountRaw",
DROP COLUMN "secondaryImagesRaw",
DROP COLUMN "storefrontPresentRaw",
DROP COLUMN "videoCountRaw",
ALTER COLUMN "imageCount" DROP NOT NULL,
ALTER COLUMN "imageCount" DROP DEFAULT,
ALTER COLUMN "videoCount" DROP NOT NULL,
ALTER COLUMN "videoCount" DROP DEFAULT,
ALTER COLUMN "salesCount" DROP NOT NULL,
ALTER COLUMN "salesCount" DROP DEFAULT;
