const prisma = require("../../database/prisma/getPrismaClient");
const { completionFactory } = require("../../services/scrapeGPT/factory");

async function getAuditMailData(csvData, clientId) {
  try {
    console.log("Getting Mail Data Information");
    const amazonData = csvData["amazonAudit"];
    const auditReport = csvData["auditReport"];
    const slug = csvData["productSlug"];
    let brandedKeyword = csvData.auditMailData?.brandedKeyword;
    let nonBrandedKeyword = csvData.auditMailData?.nonBrandedKeyword;
    let mainImageOptimisationText =
      csvData.auditMailData?.mainImageOptimisationText;
    // console.log({ amazonData, auditReport, slug });
    if (
      !csvData ||
      !csvData.auditMailData ||
      Object.keys(csvData.auditMailData).length === 0 ||
      !brandedKeyword || // Check if brandedKeyword is empty
      !nonBrandedKeyword || // Check if nonBrandedKeyword is empty
      !mainImageOptimisationText // Check if mainImageOptimisationText is empty
    ) {
      console.log("Fetching new audit mail data...");

      if (!brandedKeyword || !nonBrandedKeyword) {
        console.log("getting keywords...");
        //Search Keywords
        const gptKeywordInput = {
          "Brand Name": amazonData.company_name,
          "Product Title": amazonData.productData[0].title,
        };
        const keywords = await getBrandedKeyword(gptKeywordInput, clientId);

        // const keywordArray = keywords.message.split("\n");
        // console.log({ keywordArray });

        // Ensure keywords is a string before calling match
        if (!keywords || typeof keywords !== 'string') {
          console.warn("Keywords is not a valid string:", keywords);
          brandedKeyword = null;
          nonBrandedKeyword = null;
        } else {
          const brandedMatch = keywords.match(
            /branded mid-tail keyword\s*-\s*(.+?)\s*non-branded long-tail keyword/i
          );
          const nonBrandedMatch = keywords.match(
            /non-branded long-tail keyword\s*-\s*(.+)/i
          );

          brandedKeyword = brandedMatch ? brandedMatch[1].trim() : null;
          nonBrandedKeyword = nonBrandedMatch
            ? nonBrandedMatch[1].trim()
            : null;
        }
      }
      if (!mainImageOptimisationText) {
        console.log("getting main image optimisation text...");
        const imageOptimisationGPTInput = {
          Image: `https://eq--assets.s3.ap-south-1.amazonaws.com/images/${slug}_page_image.png`,
          Title: amazonData.productData[0].title,
          "About this item": amazonData.productData[0].description,
        };
        // console.log({ imageOptimisationGPTInput });
        mainImageOptimisationText = await getMainImageOptimisation(
          imageOptimisationGPTInput,
          clientId
        );
      }

      // // Define priority order of data points
      const priorityOrder = [
        "Videos",
        "Images",
        "Product Title",
        "A+ Content",
        "Bullet Points",
        "Ratings",
        "Reviews",
        "Storefront",
      ];

      // Initialize an empty array to store selected data points
      const selectedDataPoints = [];
      // Iterate over each priority in the order defined
      for (let dataPoint of priorityOrder) {
        // Skip Ratings if more than 4.7
        if (!auditReport[dataPoint]) {
          continue;
        }
        if (
          dataPoint === "Ratings" &&
          parseFloat(auditReport[dataPoint].Logic[0]) > 4.7
        )
          continue;

        // Skip Reviews if more than 70
        if (
          dataPoint === "Reviews" &&
          parseInt(auditReport[dataPoint].Logic[0]) > 70
        )
          continue;

        // Push data if it meets criteria and limit to top 3 data points
        if (auditReport[dataPoint] && selectedDataPoints.length < 3) {
          const topImprovement =
            dataPoint === "Ratings"
              ? auditReport[dataPoint].Improvements.at(-1) // Select last improvement for Ratings
              : auditReport[dataPoint].Improvements[0]; // Default: select first improvement

          selectedDataPoints.push({
            dataPoint,
            painPoint: auditReport[dataPoint].PAIN_POINT[0],
            topImprovement,
          });
        }
      }

      // // Create result object based on required column names
      const result = {
        Data_Point_1: selectedDataPoints[0]?.dataPoint || "",
        Pain_Point_1: selectedDataPoints[0]?.painPoint || "",
        Top_Improvement_1: selectedDataPoints[0]?.topImprovement || "",
        Data_Point_2: selectedDataPoints[1]?.dataPoint || "",
        Pain_Point_2: selectedDataPoints[1]?.painPoint || "",
        Top_Improvement_2: selectedDataPoints[1]?.topImprovement || "",
        Data_Point_3: selectedDataPoints[2]?.dataPoint || "",
        Pain_Point_3: selectedDataPoints[2]?.painPoint || "",
        Top_Improvement_3: selectedDataPoints[2]?.topImprovement || "",
        mainImageOptimisationText,
        brandedKeyword,
        nonBrandedKeyword,
      };
      // console.log({ result });
      return result;
    } else {
      console.log("Mail Data Already Present!");
      // console.log("Audit Mail Data:", csvData.auditMailData);
      return csvData.auditMailData;
    }
  } catch (error) {
    console.error("Error processing audit mail data:", error);
    return {};
  }
}

module.exports = getAuditMailData;

// Testing the function with auditReport

async function getBrandedKeyword(gptKeywordInput, clientId) {
  try {
    const options = {
      functionName: "getBrandedKeyword",
      customTags: [
        `clientId:${clientId}`,
        `factoryType:ppcAudit`,
      ],
    };
    const keywords = await completionFactory(
      "ppcAudit",
      JSON.stringify(gptKeywordInput),
      clientId,
      options
    );
    
    // Ensure keywords exists and has a message property
    if (!keywords || !keywords.message) {
      console.warn("No keywords message received from completionFactory");
      return "";
    }
    
    return keywords.message.toLowerCase();
  } catch (error) {
    console.error("Error fetching keywords:", error);
    return (keywords = { message: "" });
  }
}

async function getMainImageOptimisation(imageOptimisationGPTInput, clientId) {
  try {
    const options = {
      functionName: "getMainImageOptimisation",
      customTags: [
        `clientId:${clientId}`,
        `factoryType:mainImageOptimisation`,
      ],
    };
    const mainImageOptimisation = await completionFactory(
      "mainImageOptimisation",
      imageOptimisationGPTInput,
      clientId,
      options
    );
    return mainImageOptimisation.message;
  } catch (error) {
    console.error("Error fetching main image optimisation text:", error);
    return "";
  }
}

async function Example() {
  try {
    const companyId = 5;
    const csvData = await prisma.outputData.findFirst({
      where: {
        companyId,
      },
      orderBy: {
        updatedAt: "desc",
      },
    });
    await getAuditMailData(csvData, 1);
  } catch (error) {
    console.log("ERROR:", error);
  }
}

// Example();
