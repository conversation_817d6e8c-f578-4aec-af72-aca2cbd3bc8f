const prisma = require("../database/prisma/getPrismaClient");

/**
 * Clean string by removing extra whitespace and trimming
 */
function cleanString(str, maxLength = null) {
    if (!str) return "";
    // Remove extra whitespace (multiple spaces, newlines, tabs) and trim
    let cleaned = str.replace(/\s+/g, ' ').trim();

    // Truncate if maxLength is specified and string exceeds it
    if (maxLength && cleaned.length > maxLength) {
        cleaned = cleaned.substring(0, maxLength);
    }

    return cleaned;
}

/**
 * Extract seller ID from Amazon seller URL with improved pattern matching
 * Supports multiple Amazon URL formats across different marketplaces
 */
function extractSellerIdFromUrl(sellerUrl) {
    if (!sellerUrl || typeof sellerUrl !== 'string') {
        return null;
    }

    try {
        // Method 1: Extract from "me=" parameter (most common format)
        // Examples: 
        // - https://www.amazon.de/-/en/s?ie=UTF8&marketplaceID=A1PA6795UKMFR9&me=A70TN3VD8H6ZZ
        // - https://www.amazon.com/s?me=A2QZMC93R3KP2X
        const meMatch = sellerUrl.match(/[?&]me=([A-Za-z0-9]{10,15})/i);
        if (meMatch && meMatch[1]) {
            return meMatch[1].trim().toUpperCase();
        }

        // Method 2: Extract from "seller=" parameter (alternative format)
        // Examples:
        // - http://amazon.com/sp?seller=A2QZMC93R3KP2X
        // - https://www.amazon.com/sp?ie=UTF8&seller=A2QZMC93R3KP2X
        const sellerMatch = sellerUrl.match(/[?&]seller=([A-Za-z0-9]{10,15})/i);
        if (sellerMatch && sellerMatch[1]) {
            return sellerMatch[1].trim().toUpperCase();
        }

        // Method 3: Extract from "merchant=" parameter (some marketplaces)
        // Examples:
        // - https://www.amazon.com/s?merchant=A2QZMC93R3KP2X
        const merchantMatch = sellerUrl.match(/[?&]merchant=([A-Za-z0-9]{10,15})/i);
        if (merchantMatch && merchantMatch[1]) {
            return merchantMatch[1].trim().toUpperCase();
        }

        // Method 4: Extract from path if it contains seller ID pattern
        // Examples:
        // - https://amazon.com/shops/seller-name-A2QZMC93R3KP2X
        // - https://www.amazon.com/shops/company-name-A2QZMC93R3KP2X
        const pathMatch = sellerUrl.match(/\/([A-Za-z0-9]{10,15})\/?$/i);
        if (pathMatch && pathMatch[1]) {
            return pathMatch[1].trim().toUpperCase();
        }

        // Method 5: Extract from "ref=" parameter with seller ID pattern
        // Examples:
        // - https://www.amazon.com/s?ref=sr_pg_1&seller=A2QZMC93R3KP2X
        const refMatch = sellerUrl.match(/[?&]ref=.*?seller=([A-Za-z0-9]{10,15})/i);
        if (refMatch && refMatch[1]) {
            return refMatch[1].trim().toUpperCase();
        }

        return null;
    } catch (error) {
        console.error("Error extracting seller ID from URL:", error);
        return null;
    }
}

/**
 * Determine marketplace from seller URL
 */
function getMarketplaceFromUrl(url) {
    if (!url) return "US"; // Default to US if no URL

    if (url.includes('.co.uk')) return "UK";
    if (url.includes('.ca')) return "CA";
    if (url.includes('.com')) return "US";
    if (url.includes('.de')) return "DE";
    if (url.includes('.fr')) return "FR";
    if (url.includes('.it')) return "IT";
    if (url.includes('.es')) return "ES";
    if (url.includes('.jp')) return "JP";
    if (url.includes('.in')) return "IN";
    if (url.includes('.au')) return "AU";

    return "US"; // Default fallback
}

/**
 * Shared function to create or update JeffDataAnalysis record
 * Can be used by both seeder and processFinalData to ensure consistency
 */
async function upsertJeffDataAnalysis(outputData, amazonAudit, productData, store, prospectDetails, sellerDetails, auditMailData, competitorDetails, auditReport) {
    try {
        // Extract the first product from the array if it exists
        const firstProduct = Array.isArray(productData) ? productData[0] : productData;

        const sellerUrl = sellerDetails?.['Seller Storefront Link'] || "";
        const marketplace = getMarketplaceFromUrl(sellerUrl);

        // Extract seller ID from different possible fields
        let sellerId = cleanString(sellerDetails?.['Amazon Seller ID'] ||
            sellerDetails?.sellerId ||
            sellerDetails?.['Seller ID'] ||
            sellerDetails?.['sellerId'] ||
            sellerDetails?.sellerid || "");

        // Validate seller ID format - Amazon seller IDs should be alphanumeric without spaces
        const isValidSellerId = sellerId && /^[A-Za-z0-9]+$/.test(sellerId.trim());

        // If seller ID is invalid (contains spaces or special chars) or empty, try to extract from URL
        if (!isValidSellerId) {
            console.log(`Invalid seller ID detected: "${sellerId}". Attempting to extract from URL: ${sellerUrl}`);
            const extractedSellerId = extractSellerIdFromUrl(sellerUrl);
            if (extractedSellerId) {
                const originalSellerId = sellerId;
                sellerId = extractedSellerId;
                // Validate the extracted seller ID as well
                isValidSellerId = /^[A-Za-z0-9]+$/.test(sellerId.trim());
                console.log(`Seller ID corrected from "${originalSellerId}" to "${sellerId}"`);
            } else {
                console.log(`Failed to extract valid seller ID from URL: ${sellerUrl}`);
            }
        }

        // STRICT VALIDATION: Reject any record without a seller ID or with invalid format
        if (!sellerId || sellerId.trim() === "" || !/^[A-Za-z0-9]+$/.test(sellerId.trim())) {
            return {
                success: false,
                error: `Invalid seller ID format: "${sellerId}". Amazon seller IDs must be alphanumeric without spaces.`
            };
        }

        const jeffDataRecord = {
            // Seller information
            sellerId: sellerId,
            marketplace: marketplace,
            sellerUrl: cleanString(sellerUrl),

            // Basic product information
            url: cleanString(firstProduct?.url || ""), // Required field in schema
            brand_name: cleanString(firstProduct?.company_name || outputData.companyName, 255),
            product_title: cleanString(firstProduct?.title || prospectDetails?.productName || ""),
            description: cleanString(firstProduct?.description || ""),
            price: firstProduct?.price ? Math.min(parseFloat(firstProduct.price), 99999999.99) : null,

            // Ratings & reviews
            rating: firstProduct?.rating?.rating ? parseFloat(firstProduct.rating.rating) : null,
            total_reviews: firstProduct?.review?.totalReviewCountInt || null,
            review_category: cleanString(firstProduct?.review?.reviewsCategory || "", 100),
            star_5_count: firstProduct?.review?.reviewPerStar?.[0]?.['5starReview'] || 0,
            star_4_count: firstProduct?.review?.reviewPerStar?.[1]?.['4starReview'] || 0,
            star_3_count: firstProduct?.review?.reviewPerStar?.[2]?.['3starReview'] || 0,
            star_2_count: firstProduct?.review?.reviewPerStar?.[3]?.['2starReview'] || 0,
            star_1_count: firstProduct?.review?.reviewPerStar?.[4]?.['1starReview'] || 0,
            sales_count: firstProduct?.sales || null,

            // Media
            main_image_url: cleanString(firstProduct?.productMainImage || ""),
            image_count: firstProduct?.images?.noOfImages || null,
            video_count: firstProduct?.images?.noOfVideos || null,

            // Metadata
            title_char_count: firstProduct?.title ? firstProduct.title.length : null,
            title_under_150_chars: firstProduct?.title ? firstProduct.title.length <= 150 : null,
            out_of_stock: firstProduct?.outOfStock || false,

            // Amazon features
            aplus_content_present: !!firstProduct?.AplusContent && Object.keys(firstProduct.AplusContent).length > 0,
            premium_aplus_present: false, // This would need specific detection logic
            brand_story_present: !!firstProduct?.AplusContent?.brandStory,
            storefront_present: store?.storefront_present || false,
            storefront_url: cleanString(store?.store_front_url || ""),

            // Revenue and qualification
            prospect_revenue: parseFloat(prospectDetails?.revenue || 0),
            revenue_difference_monthly: parseFloat(outputData.revenueDifference || 0),
            revenue_difference_yearly: parseFloat((outputData.revenueDifference || 0) * 12),
            revenue_source: cleanString(prospectDetails?.revenueSource || "", 255),

            // Company and job information
            company_name: cleanString(outputData.companyName || "", 255),
            company_name_humanized: cleanString(prospectDetails?.humanizedProspectName || ""),
            campaign_name: cleanString(outputData.campaignName || "", 255),

            // Product details
            product_slug: cleanString(outputData.productSlug || "", 255),
            product_amazon_url: cleanString(prospectDetails?.productAmazonURL || ""),
            product_title_humanized: cleanString(prospectDetails?.humanizedProspectProductTitle || ""),
            product_asin: cleanString(firstProduct?.asin || "", 50),

            // Search and category
            branded_keyword: cleanString(auditMailData?.brandedKeyword || "", 255),
            non_branded_keyword: cleanString(auditMailData?.nonBrandedKeyword || "", 255),

            // Competitor information
            competitor_product_url: cleanString(competitorDetails?.productAmazonURL || ""),
            competitor_asin: cleanString(competitorDetails?.asin || "", 50),
            competitor_product_title: cleanString(competitorDetails?.productName || ""),
            competitor_product_title_humanized: cleanString(competitorDetails?.humanizedCompProductTitle || ""),
            competitor_brand_name: cleanString(competitorDetails?.name || "", 255),
            competitor_brand_name_humanized: cleanString(competitorDetails?.humanizedCompCompanyName || ""),
            competitor_revenue: parseFloat(competitorDetails?.revenue || 0),
            competitor_annual_revenue: parseFloat((competitorDetails?.revenue || 0) * 12),

            // Audit analysis fields
            number_of_optimizations: Object.keys(auditReport || {}).length,
            main_image_optimization: cleanString(auditMailData?.mainImageOptimisationText || ""),
            data_point_1: cleanString(auditMailData?.Data_Point_1 || ""),
            data_point_2: cleanString(auditMailData?.Data_Point_2 || ""),
            data_point_3: cleanString(auditMailData?.Data_Point_3 || ""),
            pain_point_1: cleanString(auditMailData?.Pain_Point_1 || ""),
            pain_point_2: cleanString(auditMailData?.Pain_Point_2 || ""),
            pain_point_3: cleanString(auditMailData?.Pain_Point_3 || ""),
            top_improvement_1: cleanString(auditMailData?.Top_Improvement_1 || ""),
            top_improvement_2: cleanString(auditMailData?.Top_Improvement_2 || ""),
            top_improvement_3: cleanString(auditMailData?.Top_Improvement_3 || ""),

            // JSON fields for complex data
            bullet_points: firstProduct?.bulletPoints || {},
            categories_and_ranks: firstProduct?.categoryAndRank || {},
            secondary_images: firstProduct?.secondaryImages || {},
            brand_story_images: firstProduct?.AplusContent?.brandStory?.finalBrandStoryArray || {},

            // Flattened category and rank fields
            category_1: cleanString(firstProduct?.categoryAndRank?.[0]?.category || "", 255),
            rank_1: firstProduct?.categoryAndRank?.[0]?.rank || null,
            category_2: cleanString(firstProduct?.categoryAndRank?.[1]?.category || "", 255),
            rank_2: firstProduct?.categoryAndRank?.[1]?.rank || null,

            // Flattened bullet point fields
            bullet_point_1: cleanString(firstProduct?.bulletPoints?.Points?.[0]?.value || ""),
            bullet_point_1_chars: firstProduct?.bulletPoints?.Points?.[0]?.NumberChars || null,
            bullet_point_2: cleanString(firstProduct?.bulletPoints?.Points?.[1]?.value || ""),
            bullet_point_2_chars: firstProduct?.bulletPoints?.Points?.[1]?.NumberChars || null,
            bullet_point_3: cleanString(firstProduct?.bulletPoints?.Points?.[2]?.value || ""),
            bullet_point_3_chars: firstProduct?.bulletPoints?.Points?.[2]?.NumberChars || null,
            bullet_point_4: cleanString(firstProduct?.bulletPoints?.Points?.[3]?.value || ""),
            bullet_point_4_chars: firstProduct?.bulletPoints?.Points?.[3]?.NumberChars || null,
            bullet_points_all_caps: firstProduct?.bulletPoints?.isItAllCaps || false,
        };

        // Always create new record (no duplicate checking for performance)
        if (outputData.productSlug) {
            const result = await prisma.jeffDataAnalysis.create({
                data: jeffDataRecord
            });
            return { success: true, record: result, action: 'created' };
        }

        return { success: false, error: 'No product_slug provided' };
    } catch (error) {
        console.error("Error upserting JeffDataAnalysis:", error);
        return { success: false, error: error.message };
    }
}

/**
 * Convenience function for processFinalData that parses JSON fields automatically
 */
async function updateJeffDataAnalysisFromProcessFinalData(csvData) {
    try {
        // Parse JSON fields if they're strings
        const amazonAudit = typeof csvData.amazonAudit === 'string'
            ? JSON.parse(csvData.amazonAudit || "{}")
            : (csvData.amazonAudit || {});

        const firstProduct = Array.isArray(amazonAudit?.productData)
            ? amazonAudit.productData[0]
            : amazonAudit?.productData || {};

        const store = amazonAudit?.store || {};
        const sellerDetails = typeof csvData.sellerDetails === 'string'
            ? JSON.parse(csvData.sellerDetails || "{}")
            : (csvData.sellerDetails || {});

        // Extract seller ID from different possible fields
        let sellerId = cleanString(sellerDetails?.['Amazon Seller ID'] ||
            sellerDetails?.sellerId ||
            sellerDetails?.['Seller ID'] ||
            sellerDetails?.['sellerId'] ||
            sellerDetails?.sellerid || "");

        // Validate seller ID format - Amazon seller IDs should be alphanumeric without spaces
        const isValidSellerId = sellerId && /^[A-Za-z0-9]+$/.test(sellerId.trim());

        // If seller ID is invalid (contains spaces or special chars) or empty, try to extract from URL
        if (!isValidSellerId) {
            const sellerUrl = sellerDetails?.['Seller Storefront Link'] || "";
            console.log(`Invalid seller ID detected: "${sellerId}". Attempting to extract from URL: ${sellerUrl}`);
            const extractedSellerId = extractSellerIdFromUrl(sellerUrl);
            if (extractedSellerId) {
                const originalSellerId = sellerId;
                sellerId = extractedSellerId;
                console.log(`Seller ID corrected from "${originalSellerId}" to "${sellerId}"`);
            } else {
                console.log(`Failed to extract valid seller ID from URL: ${sellerUrl}`);
            }
        }

        // STRICT VALIDATION: Reject any record without a seller ID or with invalid format
        if (!sellerId || sellerId.trim() === "" || !/^[A-Za-z0-9]+$/.test(sellerId.trim())) {
            return {
                success: false,
                error: `Invalid seller ID format: "${sellerId}". Amazon seller IDs must be alphanumeric without spaces.`
            };
        }

        const auditMailData = csvData.auditMailData || {};
        const prospectDetails = csvData.prospectDetails || {};
        const competitorDetails = csvData.competitorDetails || {};
        const auditReport = csvData.auditReport || {};

        return await upsertJeffDataAnalysis(
            csvData,
            amazonAudit,
            firstProduct,
            store,
            prospectDetails,
            sellerDetails,
            auditMailData,
            competitorDetails,
            auditReport
        );
    } catch (error) {
        console.error("Error in updateJeffDataAnalysisFromProcessFinalData:", error);
        return { success: false, error: error.message };
    }
}

/**
 * Utility function to find records without sellerId (for cleanup purposes)
 */
async function findRecordsWithoutSellerId() {
    try {
        const records = await prisma.jeffDataAnalysis.findMany({
            where: {
                OR: [
                    { sellerId: null },
                    { sellerId: "" },
                    { sellerId: { equals: "" } }
                ]
            },
            select: {
                id: true,
                product_slug: true,
                sellerId: true,
                created_at: true
            }
        });

        return { success: true, records, count: records.length };
    } catch (error) {
        console.error("Error finding records without sellerId:", error);
        return { success: false, error: error.message };
    }
}

/**
 * Utility function to delete records without sellerId (for cleanup purposes)
 */
async function deleteRecordsWithoutSellerId() {
    try {
        const result = await prisma.jeffDataAnalysis.deleteMany({
            where: {
                OR: [
                    { sellerId: null },
                    { sellerId: "" },
                    { sellerId: { equals: "" } }
                ]
            }
        });

        return { success: true, deletedCount: result.count };
    } catch (error) {
        console.error("Error deleting records without sellerId:", error);
        return { success: false, error: error.message };
    }
}

module.exports = {
    getMarketplaceFromUrl,
    extractSellerIdFromUrl,
    upsertJeffDataAnalysis,
    updateJeffDataAnalysisFromProcessFinalData,
    findRecordsWithoutSellerId,
    deleteRecordsWithoutSellerId
};
