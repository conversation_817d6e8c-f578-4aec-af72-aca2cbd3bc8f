const { clearCache } = require("../../services/cache/store");
const reloadServer = require("../../worker/reloadServer");
const { processJob } = require("../../worker/updateCSVData");
const { closeBrowsers } = require("../puppeteer/browserHelper");
const prisma = require("../../database/prisma/getPrismaClient");
const { processLexImageGenJob } = require("../../worker/lexImageGenWorker");
const processLexReviewCheckerJob = require("../../services/lex/reviewChecker/processLexReviewChecker");
const {
  processSingleAsinJob,
  processSellerJob,
} = require("../../worker/lexWorker");
const { MultiProviderAI } = require("../../services/ai/multiProviderAI");
const { generateReviewImage } = require("../lexUtils/generateLexImage");
const { uploadImage } = require("../../services/aws/s3");
const { sendMessageToSlack } = require("../slack");
const { LEX_TAGGED_USERS } = require("../../services/scrapeAmazon/constant");
const fs = require("fs");
const yaml = require("js-yaml");
const path = require("path");

// Model pricing per 1000 tokens (adjust based on actual pricing)
const MODEL_PRICING = {
  "azure-gpt4o": {
    input: 0.0025, // $0.0025 per 1K input tokens
    output: 0.01, // $0.01 per 1K output tokens
  },
  "gpt-4.1-jeff": {
    input: 0.002,
    output: 0.008,
  },
  "gpt-4.1-mini-jeff": {
    input: 0.0004,
    output: 0.0016,
  },
  "gemini-2.5-flash": {
    input: 0.0003,
    output: 0.0025,
  },
  "gemini-2.5-pro": {
    input: 0.00125,
    output: 0.01,
  },
  // Default fallback pricing
  default: {
    input: 0.002,
    output: 0.006,
  },
};

/**
 * Calculate cost based on model and token usage
 */
function calculateCost(model, promptTokens, completionTokens) {
  const pricing = MODEL_PRICING[model] || MODEL_PRICING.default;

  const inputCost = (promptTokens / 1000) * pricing.input;
  const outputCost = (completionTokens / 1000) * pricing.output;

  return {
    inputCost: parseFloat(inputCost.toFixed(6)),
    outputCost: parseFloat(outputCost.toFixed(6)),
    totalCost: parseFloat((inputCost + outputCost).toFixed(6)),
  };
}

async function handleSingleJeffJob(data) {
  console.log(`📦 Running Single Jeff job for client ${data.clientId}`);
  try {
    const job = await prisma.job.findFirst({
      where: {
        id: data.jobId,
      },
    });
    await processJob(job);
    clearCache();
    await closeBrowsers();
    // await reloadServer();
  } catch (error) {
    console.error("Error in handleBulkJeffJob:", error);
    console.error("Error Stack:", error.stack);
  }
}

async function handleBulkJeffJob(data) {
  console.log(`📝 Running Bulk Jeff job for client ${data.clientId}`);
  try {
    const job = await prisma.job.findFirst({
      where: {
        id: data.jobId,
      },
    });
    const serverID = data.serverID;
    await processJob(job, serverID);
    clearCache();
    await closeBrowsers();
    // await reloadServer();
  } catch (error) {
    console.error("Error in handleBulkJeffJob:", error);
    console.error("Error Stack:", error.stack);
  }
}

async function handleLexImageGen(data) {
  try {
    console.log("Running Lex Image Generator");
    const jobId = data.jobId;
    await processLexImageGenJob(jobId);
  } catch (error) {
    console.error("Error in handleLexImageGen:", error);
    console.error("Error Stack:", error.stack);
  }
}

async function handleSingleSellerScraping(data) {
  console.log("🔍 Running Single Seller Scraping");
  const { sellerId, countryCode = "US" } = data;

  try {
    const asinResults = await processSellerJob(data);
    return asinResults;
    // TODO: Add logic to scrape a single seller
  } catch (error) {
    console.error("Error in handleSingleSellerScraping:", error);
    console.error("Error Stack:", error.stack);
  }
}

async function handleSingleAsinScraping(data) {
  console.log("📦 Running Single ASIN Scraping");

  try {
    const asinId = data.asin;

    if (!asinId) {
      throw new Error("ASIN ID is required in job data");
    }

    const asinRecord = await prisma.lexASIN.findUnique({
      where: { id: asinId, countryCode: data.countryCode || "US" },
    });

    if (!asinRecord) {
      throw new Error(`ASIN with ID ${asinId} not found in DB`);
    }

    await processSingleAsinJob({
      asin: asinRecord.asin,
      countryCode: asinRecord.countryCode || "US",
    });
  } catch (error) {
    console.error("❌ Error in handleSingleAsinScraping:", error);
    console.error("Error Stack:", error.stack);
  }
}

async function handleBulkAsinScraping(data) {
  console.log("📦 Running Bulk ASIN Scraping");

  try {
    const asinIds = data.asins;

    if (!asinIds || asinIds.length === 0) {
      throw new Error("ASIN IDs are required in job data");
    }

    const asinRecords = await prisma.lexASIN.findMany({
      where: {
        id: { in: asinIds },
        countryCode: data.countryCode || "US",
      },
    });

    if (asinRecords.length === 0) {
      throw new Error("No ASINs found for provided IDs");
    }

    for (const asinRecord of asinRecords) {
      try {
        await processSingleAsinJob({
          asin: asinRecord.asin,
          countryCode: asinRecord.countryCode || "US",
        });
      } catch (err) {
        console.error(`❌ Failed to process ASIN ${asinRecord.asin}:`, err);
      }
    }

    console.log("✅ Finished Bulk ASIN Scraping");
  } catch (error) {
    console.error("❌ Error in handleBulkAsinScraping:", error);
    console.error("Error Stack:", error.stack);
  }
}

async function handleSingleReviewScraping(data) {
  console.log("📝 Running Single Review Scraping");
  try {
    const { asin, countryCode = "US", sortByMostRecent = true } = data;

    // Mark ASIN as in progress
    await prisma.lexASIN.update({
      where: { asin, countryCode },
      data: { status: "REVIEW_PENDING" },
    });
    console.log(
      `Updated asin-review status for ASIN: ${asin} in ${countryCode}`
    );
  } catch (error) {
    console.error("Error in handleSingleReviewScraping:", error);
    console.error("Error Stack:", error.stack);

    // Mark ASIN as failed if provided
    if (data.asin) {
      try {
        await prisma.lexASIN.update({
          where: { asin: data.asin, countryCode: data.countryCode || "US" },
          data: { status: "FAILED" },
        });
      } catch (updateError) {
        console.error("Failed to update ASIN status:", updateError);
      }
    }
  }
}

async function handleBulkReviewScraping(data) {
  console.log("📚 Running Bulk Review Scraping");
  try {
    const { asins, sortByMostRecent = true } = data;

    if (!asins || asins.length === 0) {
      throw new Error("No ASINs provided for bulk review scraping");
    }

    console.log(`Processing ${asins.length} ASINs for review scraping...`);

    for (const asinData of asins) {
      try {
        const { asin, countryCode } = asinData;

        // Mark ASIN as in progress using compound unique key
        await prisma.lexASIN.update({
          where: {
            asin_countryCode: {
              asin: asin,
              countryCode: countryCode,
            },
          },
          data: { status: "REVIEW_PENDING" },
        });

        console.log(
          `Updated asin-review status for ASIN: ${asin} in ${countryCode}`
        );
        await sleep(1000);
      } catch (err) {
        console.error(
          `❌ Failed to process ASIN ${asinData.asin} (${asinData.countryCode}) at handleBulkReviewScraping:`,
          err
        );

        // Mark this ASIN as failed using compound unique key
        try {
          await prisma.lexASIN.update({
            where: {
              asinCountryCode: {
                asin: asinData.asin,
                countryCode: asinData.countryCode,
              },
            },
            data: { status: "FAILED" },
          });
        } catch (updateError) {
          console.error(
            `Failed to update status for ASIN ${asinData.asin}:`,
            updateError
          );
        }
      }
    }

    console.log(
      `✅ Finished processing ${asins.length} ASINs for bulk review scraping`
    );
  } catch (error) {
    console.error("❌ Error in handleBulkReviewScraping:", error);
    console.error("Error Stack:", error.stack);
  }
}

async function handleLexReviewChecker(data) {
  console.log("📝 Running Lex Review Checker");
  try {
    const { jobId } = data;
    const job = await prisma.lexReviewCheckerJob.findUnique({
      where: { id: jobId },
    });
    if (!job) {
      throw new Error(`Lex Review Checker job with ID ${jobId} not found`);
    }
    await processLexReviewCheckerJob(job);
  } catch (error) {
    console.error("Error in handleLexReviewChecker:", error);
    console.error("Error Stack:", error.stack);
  }
}

function sleep(ms) {
  return new Promise((res) => setTimeout(res, ms));
}

/**
 * Replace variables in prompt content with values from review data
 */
function replacePromptVariables(promptContent, reviewData) {
  let result = promptContent;

  const variables = {
    reviewContent: reviewData.reviewContent || "",
    reviewTitle: reviewData.reviewTitle || "",
    productTitle: reviewData.productTitle || "",
    productLink: reviewData.productLink || "",
    reviewScore: reviewData.reviewScore || "",
    reviewer: reviewData.reviewer || "",
    reviewerCountry: reviewData.reviewerCountry || "",
    asin: reviewData.asin || "",
    sellerId: reviewData.sellerId || "",
    reviewDate: reviewData.reviewDate || "",
    variant_0: reviewData.variant_0 || "",
    variant_1: reviewData.variant_1 || "",
    // Add prompt outputs for chaining
    prompt1Output: reviewData.prompt1Output || "",
    prompt2Output: reviewData.prompt2Output || "",
    previousOutput: reviewData.previousOutput || "",
    // Add guideline-related variables for step 3
    guidelineViolation1: reviewData.guidelineViolation1 || "",
    guidelineViolation1LookupText:
      reviewData.guidelineViolation1LookupText || "",
    confidenceScore1: reviewData.confidenceScore1 || "",
    reason1: reviewData.reason1 || "",
    // Add input alias for backward compatibility
    input: reviewData.input || reviewData.reviewContent || "",
  };

  for (const [key, value] of Object.entries(variables)) {
    const regex = new RegExp(`{{${key}}}`, "g");

    // Add newline after prompt1Output and prompt2Output
    if (key === "prompt1Output" || key === "prompt2Output") {
      result = result.replace(regex, (value || "") + "\n");
    } else {
      result = result.replace(regex, value || "");
    }
    // result = result.replace(regex, value || "");
  }

  return result;
}

// Load guideline definitions
function loadGuidelineDefinitions() {
  try {
    const yamlPath = path.join(
      __dirname,
      "../../prompts/lex/guidelineDefinitions.yaml"
    );
    const fileContents = fs.readFileSync(yamlPath, "utf8");
    return yaml.load(fileContents);
  } catch (error) {
    console.error("Error loading guideline definitions:", error);
    return null;
  }
}

/**
 * Extract top violations from compliance output
 */
function extractTopViolations(complianceOutput) {
  const topViolations = {
    GuidelineViolation1: "",
    GuidelineViolation2: "",
    GuidelineViolation3: "",
    ConfidenceScore1: 0,
    ConfidenceScore2: 0,
    ConfidenceScore3: 0,
    Reason1: "",
    Reason2: "",
    Reason3: "",
  };

  // Try to extract from JSON-like structure first
  const jsonMatch = complianceOutput.match(/"topViolations":\s*{([^}]+)}/s);
  if (jsonMatch) {
    const jsonContent = jsonMatch[1];

    // Extract each field using regex
    const extractField = (fieldName) => {
      const regex = new RegExp(`"${fieldName}":\\s*"([^"]*)"`, "i");
      const match = jsonContent.match(regex);
      return match ? match[1].trim() : "";
    };

    const extractScore = (fieldName) => {
      const regex = new RegExp(`"${fieldName}":\\s*(\\d+)`, "i");
      const match = jsonContent.match(regex);
      return match ? parseInt(match[1]) : 0;
    };

    topViolations.GuidelineViolation1 = extractField("GuidelineViolation1");
    topViolations.GuidelineViolation2 = extractField("GuidelineViolation2");
    topViolations.GuidelineViolation3 = extractField("GuidelineViolation3");
    topViolations.ConfidenceScore1 = extractScore("ConfidenceScore1");
    topViolations.ConfidenceScore2 = extractScore("ConfidenceScore2");
    topViolations.ConfidenceScore3 = extractScore("ConfidenceScore3");
    topViolations.Reason1 = extractField("Reason1");
    topViolations.Reason2 = extractField("Reason2");
    topViolations.Reason3 = extractField("Reason3");

    return topViolations;
  }

  // Fallback to original regex format (legacy format)
  const topRegex =
    /(?:[-*'"\s]*)?(?:\*+)?TOP\s*([1-3])(?:\*+)?(?::|-|\s)\s*G(\d+)\s*[-–—]{1,2}\s*(\d+)\s*[-–—]{1,2}\s*(.+?)(?=(?:\n.*?(?:[-*'"\s]*)?(?:\*+)?TOP\s*[1-3])|\n*$|'.*?\+|`.*?\+)/gis;

  const matches = [...complianceOutput.matchAll(topRegex)];

  for (const match of matches) {
    const index = parseInt(match[1]); // 1, 2, or 3
    const guideline = `G${match[2]}`;
    const score = parseInt(match[3], 10);

    // Clean up the reason
    const reason = match[4]
      .trim()
      .replace(/['"`]+/g, "") // Remove quotes
      .replace(/\*+$/g, "") // Remove trailing asterisks
      .replace(/^\*+/g, "") // Remove leading asterisks
      .replace(/\s*\+\s*$/g, "") // Remove trailing + symbols
      .replace(/\s+/g, " ") // Normalize whitespace
      .replace(/\n/g, " ") // Replace line breaks with spaces
      .trim();

    topViolations[`GuidelineViolation${index}`] = guideline;
    topViolations[`ConfidenceScore${index}`] = score;
    topViolations[`Reason${index}`] = reason;
  }

  // Alternative: Try to extract from TOP_VIOLATIONS section if no JSON found
  if (
    !jsonMatch &&
    Object.values(topViolations).every((v) => v === "" || v === 0)
  ) {
    const topViolationsSection = complianceOutput.match(
      /TOP_VIOLATIONS[:\s]*[\s\S]*?(?=\n\n|\n---|$)/i
    );
    if (topViolationsSection) {
      const sectionText = topViolationsSection[0];

      // Look for patterns like "GuidelineViolation1": "G10 - Profanity/Harassment"
      const violationRegex = /"GuidelineViolation(\d+)":\s*"([^"]*?)"/g;
      const scoreRegex = /"ConfidenceScore(\d+)":\s*(\d+)/g;
      const reasonRegex = /"Reason(\d+)":\s*"([^"]*?)"/g;

      let match;
      while ((match = violationRegex.exec(sectionText)) !== null) {
        const index = match[1];
        topViolations[`GuidelineViolation${index}`] = match[2].trim();
      }

      while ((match = scoreRegex.exec(sectionText)) !== null) {
        const index = match[1];
        topViolations[`ConfidenceScore${index}`] = parseInt(match[2]);
      }

      while ((match = reasonRegex.exec(sectionText)) !== null) {
        const index = match[1];
        topViolations[`Reason${index}`] = match[2].trim();
      }
    }
  }

  return topViolations;
}

/**
 * Execute integrated prompt chain with violation detection based on confidence score
 */
async function executeIntegratedPromptChain(
  promptChain,
  reviewData,
  runTimeModel
) {
  const aiService = new MultiProviderAI();
  const guidelines = loadGuidelineDefinitions();

  // Prepare input data with all review variables
  const inputData = {
    reviewContent: reviewData.reviewContent || "",
    reviewTitle: reviewData.reviewTitle || "",
    productTitle: reviewData.productTitle || "",
    productLink: reviewData.productLink || "",
    reviewScore: reviewData.reviewScore || "",
    reviewer: reviewData.reviewer || "",
    reviewerCountry: reviewData.reviewerCountry || "",
    asin: reviewData.asin || "",
    sellerId: reviewData.sellerId || "",
    reviewDate: reviewData.reviewDate || "",
    variant_0: reviewData.variant_0 || "",
    variant_1: reviewData.variant_1 || "",
    input: reviewData.reviewContent || "",
  };
  console.log("runTimeModel", runTimeModel);
  const model = runTimeModel || promptChain.model || "azure-gpt4o";
  console.log(`🤖 Using model: ${model} for integrated prompt chain execution`);

  let prompt1Output = "";
  let prompt2Output = "";
  let prompt3Output = "";
  let violationData = null;
  let topViolations = null;
  let guidelineViolation1LookupText = "";

  // Store prompt inputs for return
  let prompt1Input = "";
  let prompt2Input = "";
  let prompt3Input = "";

  let totalPromptTokens = 0;
  let totalCompletionTokens = 0;

  // Step 1: Execute Prompt 1
  console.log("🔄 Executing Prompt 1...");
  const step1Variables = { ...inputData };
  const prompt1Content = replacePromptVariables(
    promptChain.prompt1,
    step1Variables
  );
  prompt1Input = prompt1Content; // Store for return

  const result1 = await aiService.executePrompt(
    model,
    "You are an expert content analyst.",
    prompt1Content,
    { temperature: 0.7, maxTokens: 1000 }
  );

  prompt1Output = result1.content;
  totalPromptTokens += result1.usage.promptTokens;
  totalCompletionTokens += result1.usage.completionTokens;
  console.log(prompt1Output, "prompt 1 output");
  console.log(`✅ Prompt 1 completed. Output length: ${prompt1Output.length}`);

  // Step 2: Execute Prompt 2 with Guideline Compliance Check
  console.log("🔄 Executing Prompt 2 (Compliance Analysis)...");
  const step2Variables = {
    ...inputData,
    previousOutput: prompt1Output,
    prompt1Output: prompt1Output,
  };
  const prompt2Content = replacePromptVariables(
    promptChain.prompt2,
    step2Variables
  );

  // Create guideline definitions text from YAML
  let guidelineText = "";
  if (guidelines && guidelines.amazon_review_guidelines) {
    const guidelineData = guidelines.amazon_review_guidelines.guidelines;
    guidelineText = Object.entries(guidelineData)
      .map(([key, guideline]) => {
        return `${key}: ${guideline.title}
DESCRIPTION: ${guideline.description}
ALLOWED: ${guideline.allowed_example}
NOT ALLOWED: ${guideline.not_allowed_example}
REASONING: ${guideline.reasoning}`;
      })
      .join("\n\n");
  }

  const compliancePromptContent = `${prompt2Content || ""}`;

  prompt2Input = compliancePromptContent; // Store for return

  const result2 = await aiService.executePrompt(
    model,
    "You are an Amazon review compliance analysis assistant.",
    compliancePromptContent,
    { temperature: 0.7, maxTokens: 1000 }
  );

  // console.log(`Prompt 2 (Compliance Analysis) Input: ${compliancePromptContent}`);

  prompt2Output = result2.content;
  totalPromptTokens += result2.usage.promptTokens;
  totalCompletionTokens += result2.usage.completionTokens;

  console.log(`✅ Prompt 2 completed. Output length: ${prompt2Output.length}`);
  console.log("Prompt 2 Output:----------------------------- ", prompt2Output);

  // Extract violation data from prompt 2 output
  topViolations = extractTopViolations(prompt2Output);

  // Step 3: Execute Prompt 3 (Violation Analysis) only if confidence score ≥ 1
  if (topViolations.ConfidenceScore1 >= 1) {
    console.log(
      `🔄 Executing Prompt 3 (Violation Analysis) - Confidence Score: ${topViolations.ConfidenceScore1}`
    );

    // Get guideline lookup text for GuidelineViolation1
    if (
      guidelines &&
      guidelines.amazon_review_guidelines &&
      topViolations.GuidelineViolation1
    ) {
      const guidelineKey = topViolations.GuidelineViolation1;
      const guidelineData =
        guidelines.amazon_review_guidelines.guidelines[guidelineKey];
      if (guidelineData) {
        guidelineViolation1LookupText = `
Title: ${guidelineData.title}
Description: ${guidelineData.description}
Allowed Example: ${guidelineData.allowed_example}
Not Allowed Example: ${guidelineData.not_allowed_example}
Reasoning: ${guidelineData.reasoning}`;
      }
    }

    let violationPromptContent;
    if (promptChain.prompt3 && promptChain.prompt3.trim()) {
      // Use custom prompt 3 from the chain
      const step3Variables = {
        ...inputData,
        previousOutput: prompt2Output,
        prompt1Output: prompt1Output,
        prompt2Output: prompt2Output,
        guidelineViolation1: topViolations.GuidelineViolation1,
        guidelineViolation1LookupText: guidelineViolation1LookupText,
        confidenceScore1: topViolations.ConfidenceScore1,
        reason1: topViolations.Reason1,
      };
      violationPromptContent = replacePromptVariables(
        promptChain.prompt3,
        step3Variables
      );
    } else {
      // Use default violation detection prompt
      violationPromptContent = `
        Based on the previous analysis, now analyze this review for policy violations:
        
        Previous Analysis 1: ${prompt1Output}
        Previous Analysis 2: ${prompt2Output}

        Top Violation Details:
        - Guideline: ${topViolations.GuidelineViolation1}
        - Confidence Score: ${topViolations.ConfidenceScore1}
        - Reason: ${topViolations.Reason1}
        - Guideline Details: ${guidelineViolation1LookupText}
        
        Original Review:
        Product: ${reviewData.productTitle || "Unknown"}
        Review Title: ${reviewData.reviewTitle || "No title"}
        Review Content: ${reviewData.reviewContent || "No content"}
        Reviewer: ${reviewData.reviewer || "Unknown"} from ${
        reviewData.reviewerCountry || "Unknown"
      }
        Rating: ${reviewData.reviewScore || "Unknown"}/5
        
        Check for:
        1. False or misleading claims
        2. Inappropriate language or content
        3. Fake review indicators
        4. Policy violations
        5. Spam or promotional content
        
        Respond with:
        - VIOLATION: true/false
        - CONFIDENCE: score from 0-100
        - REASON: brief explanation if violation found
        - CATEGORY: type of violation if any (false_claims, inappropriate_content, fake_review, policy_violation, spam)
        
        Format your response as JSON.
      `;
    }

    prompt3Input = violationPromptContent; // Store for return

    const result3 = await aiService.executePrompt(
      model,
      "You are an expert content moderator. Analyze for policy violations based on the previous analysis.",
      violationPromptContent,
      { temperature: 0.1, maxTokens: 500 }
    );
    // console.log(`Prompt 3 (Vio@etection) Input: ${violationPromptContent}`);
    console.log(
      "Prompt 3 Output:----------------------------- ",
      result3.content
    );
    prompt3Output = result3.content;
    totalPromptTokens += result3.usage.promptTokens;
    totalCompletionTokens += result3.usage.completionTokens;

    // Parse violation data from prompt 3 output
    try {
      violationData = JSON.parse(result3.content);
    } catch (parseError) {
      // Fallback parsing if JSON is malformed
      const content = result3.content.toLowerCase();
      violationData = {
        violation:
          content.includes("violation: true") ||
          content.includes('"violation":true'),
        confidence: 50,
        reason: result3.content.substring(0, 200),
        category: "unknown",
      };
    }

    console.log(
      `✅ Prompt 3 (Violation Detection) completed. Violation: ${violationData.violation}, Confidence: ${violationData.confidence}%`
    );
  } else {
    console.log(
      `⏭️ Skipping Prompt 3 (Violation Analysis) - Confidence Score: ${topViolations.ConfidenceScore1} < 1`
    );

    // Set empty string for prompt3Output and default no-violation data
    prompt3Output = "";
    prompt3Input = ""; // No prompt 3 input since it was skipped
    violationData = {
      violation: false,
      confidence: 0,
      reason: "No significant violations detected in initial analysis",
      category: "none",
    };
  }

  return {
    prompt1Output,
    prompt2Output,
    prompt3Output,
    violationData,
    tokenUsage: {
      promptTokens: totalPromptTokens,
      completionTokens: totalCompletionTokens,
      totalTokens: totalPromptTokens + totalCompletionTokens,
    },
    model,
    // New return values
    prompt1Input,
    prompt2Input,
    prompt3Input,
    topViolations,
    guidelineViolation1LookupText,
  };
}

/**
 * Handle single prompt chain execution job
 */
async function handleSinglePromptChainJob(data) {
  console.log("🔗 Running Single Integrated Prompt Chain Execution");

  try {
    const { promptChainId, reviewId, model, id: jobId } = data;

    // Get the prompt chain
    const promptChain = await prisma.lexPromptChain.findUnique({
      where: { id: promptChainId },
    });

    if (!promptChain) {
      throw new Error(`Prompt chain with ID ${promptChainId} not found`);
    }

    if (!promptChain.isActive) {
      throw new Error(`Prompt chain "${promptChain.name}" is not active`);
    }

    // Get the review data
    const review = await prisma.lexReview.findUnique({
      where: { id: reviewId },
    });

    if (!review) {
      throw new Error(`Review with ID ${reviewId} not found`);
    }

    // Execute the integrated prompt chain (1, 2, then violation detection)
    const result = await executeIntegratedPromptChain(
      promptChain,
      review,
      model
    );

    // Calculate accurate cost using model pricing
    const costDetails = calculateCost(
      result.model,
      result.tokenUsage.promptTokens,
      result.tokenUsage.completionTokens
    );

    // Update the review with basic status only
    await prisma.lexReview.update({
      where: { id: reviewId },
      data: {
        prompt1Output: result.prompt1Output,
        prompt2Output: result.prompt2Output,
        prompt3Output: result.prompt3Output,
        violation: result.violationData?.violation || false,
        violationConfidence: result.violationData?.confidence || 0,
        violationReason: result.violationData?.reason || null,
        status: result.violationData?.violation
          ? "VIOLATION_DETECTED"
          : "NO_VIOLATION_DETECTED",
        lastPromptChainId: promptChainId,
      },
    });

    // Store comprehensive output metadata in jobCentral
    const outputMetadata = {
      reviewId: reviewId,
      promptChainId: promptChainId,
      model: result.model,
      executionTimestamp: new Date().toISOString(),
      tokenUsage: result.tokenUsage,
      cost: costDetails, // Include cost details in metadata
      outputs: {
        prompt1Output: result.prompt1Output,
        prompt2Output: result.prompt2Output,
        prompt3Output: result.prompt3Output,
      },
      inputs: {
        prompt1Input: result.prompt1Input,
        prompt2Input: result.prompt2Input,
        prompt3Input: result.prompt3Input,
      },
      topViolations: result.topViolations,
      guidelineViolation1LookupText: result.guidelineViolation1LookupText,
      violationData: result.violationData,
      reviewData: {
        reviewID: review.reviewID,
        asin: review.asin,
        productTitle: review.productTitle,
        status: result.violationData?.violation
          ? "VIOLATION_DETECTED"
          : "NO_VIOLATION_DETECTED",
      },
      performance: {
        totalTokens: result.tokenUsage.totalTokens,
        totalCost: costDetails.totalCost,
        processingTime: new Date().toISOString(),
      },
    };

    // Update jobCentral with output metadata (cost included in metadata)
    await prisma.jobCentral.update({
      where: { id: jobId },
      data: {
        params: {
          ...data,
          outputMetadata: outputMetadata,
        },
      },
    });

    console.log(`📝 Stored output metadata in jobCentral for job ${jobId}:`, {
      reviewId: reviewId,
      totalCost: costDetails.totalCost,
      totalTokens: result.tokenUsage.totalTokens,
      violation: result.violationData?.violation,
    });

    console.log(`✅ Prompt chain execution completed for review ${reviewId}`);

    return {
      success: true,
      reviewId,
      promptChainId,
      result,
      cost: costDetails,
    };
  } catch (error) {
    console.error("❌ Error in handleSinglePromptChainJob:", error);
    console.error("Error Stack:", error.stack);
    throw error;
  }
}

/**
 * Handle single violation detection job
 */
async function handleSingleViolationDetectionJob(data) {
  console.log("🔍 Running Single Violation Detection");

  try {
    const { reviewId, model = "azure-gpt4o" } = data;

    // Get the review data
    const review = await prisma.lexReview.findUnique({
      where: { id: reviewId },
    });

    if (!review) {
      throw new Error(`Review with ID ${reviewId} not found`);
    }

    if (!review.lastPromptChainId) {
      throw new Error(
        `Review with ID ${reviewId} has no associated prompt chain for violation detection.`
      );
    }

    // Get the prompt chain to retrieve prompt3
    const promptChain = await prisma.lexPromptChain.findUnique({
      where: { id: review.lastPromptChainId },
    });

    if (!promptChain) {
      throw new Error(
        `Prompt chain with ID ${review.lastPromptChainId} not found for review ${reviewId}`
      );
    }

    // Update status to in progress
    await prisma.lexReview.update({
      where: { id: reviewId },
      data: {
        status: "VIOLATION_DETECTION_IN_PROGRESS",
        metadata: {
          ...review.metadata,
          violationDetection: {
            model,
            status: "IN_PROGRESS",
            timestamp: new Date().toISOString(),
          },
        },
      },
    });

    const aiService = new MultiProviderAI();

    // Prepare input data for prompt 3
    const inputData = {
      reviewContent: review.reviewContent || "",
      reviewTitle: review.reviewTitle || "",
      productTitle: review.productTitle || "",
      productLink: review.productLink || "",
      reviewScore: review.reviewScore || "",
      reviewer: review.reviewer || "",
      reviewerCountry: review.reviewerCountry || "",
      asin: review.asin || "",
      sellerId: review.sellerId || "",
      reviewDate: review.reviewDate || "",
      variant_0: review.variant_0 || "",
      variant_1: review.variant_1 || "",
      prompt1Output: review.prompt1Output || "",
      prompt2Output: review.prompt2Output || "",
    };

    const violationPromptContent = replacePromptVariables(
      promptChain.prompt3,
      inputData
    );

    const result = await aiService.executePrompt(
      model,
      "You are an expert content moderator analyzing product reviews for violations.",
      violationPromptContent,
      { temperature: 0.1, maxTokens: 500 }
    );

    // Parse the AI response
    let violationData;
    try {
      violationData = JSON.parse(result.content);
    } catch (parseError) {
      // Fallback parsing if JSON is malformed
      const content = result.content.toLowerCase();
      violationData = {
        violation:
          content.includes("violation: true") ||
          content.includes('"violation":true'),
        confidence: 50,
        reason: result.content.substring(0, 200),
        category: "unknown",
      };
    }

    const hasViolation = violationData.violation || false;
    const confidence = violationData.confidence || 0;
    const reason = violationData.reason || "No specific reason provided";

    // Calculate cost (simplified - you may want to use your existing pricing calculation)
    const cost = result.usage.totalTokens * 0.00002; // Rough estimate

    // Update the review with results
    await prisma.lexReview.update({
      where: { id: reviewId },
      data: {
        violation: hasViolation,
        violationConfidence: confidence,
        violationReason: reason,
        status: hasViolation ? "VIOLATION_DETECTED" : "NO_VIOLATION_DETECTED",
        metadata: {
          ...review.metadata,
          violationDetection: {
            ...review.metadata.violationDetection,
            status: "COMPLETED",
            cost,
            promptTokens: result.usage.promptTokens,
            completionTokens: result.usage.completionTokens,
            totalTokens: result.usage.totalTokens,
            output: result.content,
            timestamp: new Date().toISOString(),
          },
        },
      },
    });

    console.log(
      `✅ Violation detection completed for review ${reviewId}. Violation: ${hasViolation}, Confidence: ${confidence}%`
    );

    return {
      success: true,
      reviewId,
      hasViolation,
      confidence,
      reason,
      cost,
      tokenUsage: result.usage,
    };
  } catch (error) {
    console.error("❌ Error in handleSingleViolationDetectionJob:", error);

    // Update review status to failed
    if (data.reviewId) {
      try {
        await prisma.lexReview.update({
          where: { id: data.reviewId },
          data: { status: "FAILED" },
        });
      } catch (updateError) {
        console.error("Failed to update review status:", updateError);
      }
    }

    throw error;
  }
}

/**
 * Handle bulk violation detection job
 */
async function handleBulkViolationDetectionJob(data) {
  console.log("🔍 Running Bulk Violation Detection");

  try {
    const { reviewIds, model = "azure-gpt4o" } = data;

    // Get the reviews
    const reviews = await prisma.lexReview.findMany({
      where: { id: { in: reviewIds } },
    });

    if (reviews.length === 0) {
      throw new Error("No reviews found for provided IDs");
    }

    const results = [];

    for (const review of reviews) {
      try {
        const singleResult = await handleSingleViolationDetectionJob({
          reviewId: review.id,
          model,
        });

        results.push(singleResult);

        // Small delay to prevent rate limiting
        await sleep(1000);
      } catch (error) {
        console.error(`❌ Failed to process review ${review.id}:`, error);
        results.push({
          reviewId: review.id,
          success: false,
          error: error.message,
        });
      }
    }

    console.log(
      `✅ Finished bulk violation detection for ${results.length} reviews`
    );

    return {
      success: true,
      results,
      totalProcessed: results.length,
    };
  } catch (error) {
    console.error("❌ Error in handleBulkViolationDetectionJob:", error);
    throw error;
  }
}

/**
 * Handle bulk prompt chain execution job - Updated for CSV metadata processing
 */
async function handleBulkPromptChainJob(data) {
  console.log("🔗 Running Bulk Integrated Prompt Chain Execution");

  try {
    const { promptChainId, reviewIds, model, id: jobId, csvMetadata } = data;

    // Get the prompt chain
    const promptChain = await prisma.lexPromptChain.findUnique({
      where: { id: promptChainId },
    });

    if (!promptChain) {
      throw new Error(`Prompt chain with ID ${promptChainId} not found`);
    }

    if (!promptChain.isActive) {
      throw new Error(`Prompt chain "${promptChain.name}" is not active`);
    }

    let reviewsToProcess = [];
    let totalCostSum = 0;
    let totalTokensSum = 0;

    // Check if this is a CSV job (has csvMetadata)
    if (csvMetadata) {
      // For CSV jobs, get review data from job metadata instead of database
      const job = await prisma.jobCentral.findUnique({
        where: { id: jobId },
      });

      if (!job) {
        throw new Error(`Job with ID ${jobId} not found`);
      }

      // Extract review data from CSV metadata stored in job params
      const csvInputMetadata = job.params?.csvMetadata || {};
      const clientReviewsData = csvInputMetadata.clientReviewsData || [];
      const nonClientReviewsData = csvInputMetadata.nonClientReviewsData || [];

      // Use the appropriate review set based on the prompt chain's isClient flag
      reviewsToProcess = promptChain.isClient
        ? clientReviewsData
        : nonClientReviewsData;

      console.log(
        `📊 Processing ${reviewsToProcess.length} reviews from CSV metadata (isClient: ${promptChain.isClient})`
      );
    } else {
      // For regular jobs, get reviews from database
      const reviews = await prisma.lexReview.findMany({
        where: { id: { in: reviewIds } },
      });

      if (reviews.length === 0) {
        throw new Error("No reviews found for provided IDs");
      }

      reviewsToProcess = reviews;
      console.log(
        `📊 Processing ${reviewsToProcess.length} reviews from database`
      );
    }

    const results = [];
    console.log(model, "hiya");
    for (const reviewData of reviewsToProcess) {
      try {
        // Execute the integrated prompt chain (1, 2, then violation detection)
        const result = await executeIntegratedPromptChain(
          promptChain,
          reviewData,
          model
        );

        // Calculate accurate cost using model pricing
        const costDetails = calculateCost(
          result.model,
          result.tokenUsage.promptTokens,
          result.tokenUsage.completionTokens
        );

        totalCostSum += costDetails.totalCost;
        totalTokensSum += result.tokenUsage.totalTokens;

        // For CSV jobs, don't update database records - store results in job metadata
        if (!csvMetadata) {
          // Update the review in database (only for non-CSV jobs)
          await prisma.lexReview.update({
            where: { id: reviewData.id },
            data: {
              prompt1Output: result.prompt1Output,
              prompt2Output: result.prompt2Output,
              prompt3Output: result.prompt3Output,
              violation: result.violationData?.violation || false,
              violationConfidence: result.violationData?.confidence || 0,
              violationReason: result.violationData?.reason || null,
              status: result.violationData?.violation
                ? "VIOLATION_DETECTED"
                : "NO_VIOLATION_DETECTED",
              lastPromptChainId: promptChainId,
            },
          });
        }

        console.log(
          `📝 Processed review ${reviewData.reviewID || reviewData.id}:`,
          {
            prompt1: result.prompt1Output
              ? `${result.prompt1Output.substring(0, 100)}...`
              : "null",
            prompt2: result.prompt2Output
              ? `${result.prompt2Output.substring(0, 100)}...`
              : "null",
            prompt3: result.prompt3Output
              ? `${result.prompt3Output.substring(0, 100)}...`
              : "null",
            violation: result.violationData?.violation,
            confidence: result.violationData?.confidence,
            cost: costDetails.totalCost,
          }
        );

        results.push({
          reviewId: reviewData.reviewID || reviewData.id,
          success: true,
          reviewData: {
            reviewID: reviewData.reviewID,
            asin: reviewData.asin,
            productTitle: reviewData.productTitle,
          },
          tokenUsage: result.tokenUsage,
          cost: costDetails,
          outputs: {
            prompt1Output: result.prompt1Output,
            prompt2Output: result.prompt2Output,
            prompt3Output: result.prompt3Output,
          },
          inputs: {
            prompt1Input: result.prompt1Input,
            prompt2Input: result.prompt2Input,
            prompt3Input: result.prompt3Input,
          },
          topViolations: result.topViolations,
          guidelineViolation1LookupText: result.guidelineViolation1LookupText,
          violationData: result.violationData,
        });

        console.log(
          `✅ Integrated prompt chain execution completed for review ${
            reviewData.reviewID || reviewData.id
          }`
        );

        // Small delay to prevent rate limiting
        await sleep(500);
      } catch (error) {
        console.error(
          `❌ Failed to process review ${
            reviewData.reviewID || reviewData.id
          }:`,
          error
        );
        results.push({
          reviewId: reviewData.reviewID || reviewData.id,
          success: false,
          error: error.message,
        });
      }
    }

    // Store comprehensive output metadata in jobCentral for bulk job
    const outputMetadata = {
      promptChainId: promptChainId,
      model: model || promptChain.model,
      executionTimestamp: new Date().toISOString(),
      bulkSummary: {
        totalReviews: reviewsToProcess.length,
        successfulReviews: results.filter((r) => r.success).length,
        failedReviews: results.filter((r) => !r.success).length,
        totalTokens: totalTokensSum,
        totalCost: totalCostSum,
      },
      results: results,
      performance: {
        averageCostPerReview:
          results.length > 0 ? totalCostSum / results.length : 0,
        averageTokensPerReview:
          results.length > 0 ? totalTokensSum / results.length : 0,
        processingTime: new Date().toISOString(),
      },
      // For CSV jobs, also include the original input data
      ...(csvMetadata && { csvSourceData: reviewsToProcess }),
    };

    // Update jobCentral with output metadata and total cost
    await prisma.jobCentral.update({
      where: { id: jobId },
      data: {
        params: {
          ...data,
          outputMetadata: outputMetadata,
        },
      },
    });

    console.log(
      `📝 Stored bulk output metadata in jobCentral for job ${jobId}:`,
      {
        totalReviews: reviewsToProcess.length,
        successfulReviews: results.filter((r) => r.success).length,
        totalCost: totalCostSum,
        totalTokens: totalTokensSum,
        isCSVJob: !!csvMetadata,
      }
    );

    console.log(
      `✅ Finished bulk prompt chain execution for ${results.length} reviews`
    );

    return {
      success: true,
      promptChainId,
      results,
      totalProcessed: results.length,
      totalCost: totalCostSum,
    };
  } catch (error) {
    console.error("❌ Error in handleBulkPromptChainJob:", error);
    console.error("Error Stack:", error.stack);
    throw error;
  }
}

async function handleReviewScreenshotGeneration(data) {
  console.log(
    `📸 Running Review Screenshot Generation job for ASIN: ${data.asin}`
  );

  try {
    const { asin, countryCode = "US" } = data;

    // Process all reviews for the ASIN that need screenshot generation
    console.log(
      `📸 Processing all reviews for ASIN ${asin} that need screenshot generation`
    );

    const reviewsToProcess = await prisma.lexReview.findMany({
      where: {
        AND: [
          { asin: asin },
          { status: "DATA_SCRAPED" },
          { reviewScreenshot: null },
          {
            asinRef: {
              type: "CLIENT",
            },
          },
        ],
      },
      include: {
        asinRef: {
          select: {
            asin: true,
            type: true,
            countryCode: true,
          },
        },
      },
    });

    if (reviewsToProcess.length === 0) {
      console.log(
        `📭 No reviews found for screenshot generation for ASIN ${asin}`
      );
      return {
        success: true,
        asin,
        totalReviews: 0,
        successCount: 0,
        failureCount: 0,
        message: "No reviews found for screenshot generation",
      };
    }

    console.log(
      `📸 Found ${reviewsToProcess.length} reviews for screenshot generation`
    );

    let successCount = 0;
    let failureCount = 0;
    const failedReviewIds = [];

    for (const review of reviewsToProcess) {
      try {
        const screenshotUrl = await generateAndUploadScreenshot(review);

        if (screenshotUrl) {
          await prisma.lexReview.update({
            where: { id: review.id },
            data: {
              reviewScreenshot: screenshotUrl,
              status: "DATA_SCRAPED_IMAGE_GEN",
              updatedAt: new Date(),
            },
          });
          successCount++;
          console.log(
            `✅ Screenshot generated for review ${review.reviewID}: ${screenshotUrl}`
          );
        } else {
          failureCount++;
          failedReviewIds.push(review.reviewID);
          console.error(
            `❌ Failed to generate screenshot for review ${review.reviewID}`
          );
        }
      } catch (error) {
        console.error(`❌ Error processing review ${review.reviewID}:`, error);
        failureCount++;
        failedReviewIds.push(review.reviewID);

        // Mark review as failed
        await prisma.lexReview.update({
          where: { id: review.id },
          data: {
            status: "FAILED",
            updatedAt: new Date(),
          },
        });
      }
    }

    console.log(
      `📸 Screenshot generation completed for ASIN ${asin}: ${successCount} success, ${failureCount} failures`
    );

    // Send consolidated notification per ASIN instead of per review
    await sendAsinScreenshotCompletionNotification(
      asin,
      countryCode,
      successCount,
      failureCount,
      failedReviewIds,
      reviewsToProcess.length
    );

    return {
      success: true,
      asin,
      totalReviews: reviewsToProcess.length,
      successCount,
      failureCount,
    };
  } catch (error) {
    console.error("❌ Error in handleReviewScreenshotGeneration:", error);
    console.error("Error Stack:", error.stack);
    throw error;
  }
}

async function generateAndUploadScreenshot(review, attempt = 1) {
  const MAX_RETRY_ATTEMPTS = 3;
  const MIN_RETRY_DELAY_MS = 3000; // 3 seconds
  const MAX_RETRY_DELAY_MS = 6000; // 6 seconds

  try {
    console.log(`📸 Attempt ${attempt} for review ${review.reviewID}`);

    if (!review.reviewLink) {
      console.warn(`⚠️ No review link found for review ${review.reviewID}`);
      return null;
    }

    // Generate screenshot using generateReviewImage function
    const screenshotBuffer = await generateReviewImage(
      review.reviewLink,
      1,
      attempt
    );

    if (!screenshotBuffer) {
      if (attempt < MAX_RETRY_ATTEMPTS) {
        // Generate random delay between 3-6 seconds
        const randomDelay = Math.floor(
          Math.random() * (MAX_RETRY_DELAY_MS - MIN_RETRY_DELAY_MS + 1) + MIN_RETRY_DELAY_MS
        );
        
        console.log(
          `🔄 Retrying screenshot generation for review ${
            review.reviewID
          } (attempt ${attempt + 1}) in ${randomDelay}ms`
        );
        await sleep(randomDelay);
        return generateAndUploadScreenshot(review, attempt + 1);
      } else {
        console.error(
          `❌ Failed to generate screenshot for review ${review.reviewID} after ${MAX_RETRY_ATTEMPTS} attempts`
        );
        return null;
      }
    }

    // Upload screenshot buffer directly to S3
    const filename = `${review.reviewID}_${Date.now()}.png`;
    const bucketName = "eq-lex";
    const imageFolder = "lex_images/";
    const image = await uploadImage(
      screenshotBuffer,
      filename,
      imageFolder,
      "eq-lex"
    );
    const screenshotUrl = `https://${bucketName}.s3.ap-south-1.amazonaws.com/${imageFolder}${filename}`;
    if (screenshotUrl) {
      console.log(`✅ Screenshot uploaded successfully: ${screenshotUrl}`);
      return screenshotUrl;
    } else {
      console.error(
        `❌ Failed to upload screenshot for review ${review.reviewID}`
      );
      return null;
    }
  } catch (error) {
    console.error(
      `❌ Error in generateAndUploadScreenshot for review ${review.reviewID}:`,
      error
    );

    if (attempt < MAX_RETRY_ATTEMPTS) {
      // Generate random delay between 3-6 seconds
      const randomDelay = Math.floor(
        Math.random() * (MAX_RETRY_DELAY_MS - MIN_RETRY_DELAY_MS + 1) + MIN_RETRY_DELAY_MS
      );
      
      console.log(
        `🔄 Retrying screenshot generation for review ${
          review.reviewID
        } (attempt ${attempt + 1}) in ${randomDelay}ms`
      );
      await sleep(randomDelay);
      return generateAndUploadScreenshot(review, attempt + 1);
    } else {
      console.error(
        `❌ Failed to generate screenshot for review ${review.reviewID} after ${MAX_RETRY_ATTEMPTS} attempts`
      );
      return null;
    }
  }
}



async function sendAsinScreenshotCompletionNotification(
  asin,
  countryCode,
  successCount,
  failureCount,
  failedReviewIds,
  totalReviews
) {
  try {
    const message =
      `📸 Review Screenshot Generation for ASIN ${asin} (${countryCode}) completed.\n` +
      `Total Reviews: ${totalReviews}\n` +
      `Success: ${successCount}\n` +
      `Failed: ${failureCount}\n` +
      `Failed Review IDs: ${failedReviewIds.join(", ")}\n` +
      `${LEX_TAGGED_USERS.join(" ")}`;

    await sendMessageToSlack(process.env.LEX_NOTI_SLACK_WEBHOOK_URL, message);
  } catch (error) {
    console.error("❌ Error sending ASIN screenshot completion notification:", error);
    // Don't throw error - we don't want to break the main screenshot generation process
  }
}

// Export handlers as a map (keys must match `scriptType`)
module.exports = {
  singleJeff: handleSingleJeffJob,
  bulkJeff: handleBulkJeffJob,
  lexImageGen: handleLexImageGen,
  singleLexSeller: handleSingleSellerScraping,
  singleLexAsin: handleSingleAsinScraping,
  bulkLexAsin: handleBulkAsinScraping,
  singleLexReview: handleSingleReviewScraping,
  bulkLexReview: handleBulkReviewScraping,
  singleLexViolationDetection: handleSingleViolationDetectionJob,
  bulkLexViolationDetection: handleBulkViolationDetectionJob,
  singleLexPromptChain: handleSinglePromptChainJob,
  bulkLexPromptChain: handleBulkPromptChainJob,
  lexReviewChecker: handleLexReviewChecker,
  reviewScreenshotGeneration: handleReviewScreenshotGeneration,
};